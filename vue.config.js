'use strict'
const path = require('path')
const webpack = require('webpack')

function resolve(dir) {
	return path.join(__dirname, dir)
}

const name = process.env.VUE_APP_TITLE || '智库管理系统' // 网页标题

const port = process.env.port || process.env.npm_config_port || 80 // 端口
// 引入 BundleAnalyzerPlugin
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
const CompressionWebpackPlugin = require('compression-webpack-plugin') // 引入插件
// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderopfile:///C:/Users/<USER>/Desktop/新建文件夹 (2)/quickDev.jstions
// 这里只列一部分，具体配置参考文档
module.exports = {
	// 部署生产环境和开发环境下的URL。
	// 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
	// 例如 https://www.coalmine.com/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.coalmine.com/flow/，则设置 baseUrl 为 /flow/。
	publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
	// 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
	outputDir: 'dist',
	// 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
	assetsDir: 'static',
	// 是否开启eslint保存检测，有效值：ture | false | 'error'
	lintOnSave: process.env.NODE_ENV === 'development',
	// 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
	productionSourceMap: false,
	// webpack-dev-server 相关配置
	devServer: {
		host: '0.0.0.0',
		port: port,
		open: true,
		proxy: {
			// detail: https://cli.vuejs.org/config/#devserver-proxy
			[process.env.VUE_APP_BASE_API]: {
				target: process.env.VUE_APP_BASE_GATE_WAY_URL,
				changeOrigin: true,
				pathRewrite: {
					['^' + process.env.VUE_APP_BASE_API]: ''
				}
			},
			'/hks': {
				target: 'http://zhibo.hkstv.tv',
				changeOrigin: true, // 支持跨域
				pathRewrite: { // 重写路径: 去掉路径中开头的'/api'
					'^/hks': ''
				}
			},
			[process.env.VUE_APP_BASE_TSl]: {
				target: 'http://************:5000/',
				changeOrigin: true, // 支持跨域
				pathRewrite: { // 重写路径: 去掉路径中开头的'/api'
					['^' + process.env.VUE_APP_BASE_TSl]: ''
				}
			},
			[process.env.VUE_APP_BASE_SQL]: {
				// target: 'http://************:7862/',
				target: 'https://u461063-b0dd-d6bab371.westc.gpuhub.com:8443/vn',
				changeOrigin: true, // 支持跨域
				pathRewrite: { // 重写路径: 去掉路径中开头的'/api'
					['^' + process.env.VUE_APP_BASE_SQL]: ''
				}
			},
			[process.env.VUE_APP_BASE_GRAPH]: {
				// target: 'http://************:7862/',
				target: 'https://u461063-b0dd-d6bab371.westc.gpuhub.com:7861/',
				changeOrigin: true, // 支持跨域
				pathRewrite: { // 重写路径: 去掉路径中开头的'/api'
					['^' + process.env.VUE_APP_BASE_GRAPH]: ''
				}
			},
			[process.env.VUE_APP_BASE_AI]: {
				// target: `http://192.168.0.150:8090`,
				//  target: `http://************:31011`,
				 target: `https://u461063-b26f-bc4667fa.westc.gpuhub.com:8443/`,
				// target: `http://192.168.0.105:8090`,
				changeOrigin: true,
				pathRewrite: {
				  ['^' + process.env.VUE_APP_BASE_AI]: ''
				}
			  },
			[process.env.VUE_APP_BASE_HELP_URL]: {
				target: 'http://**************/',
				changeOrigin: true, // 支持跨域
				pathRewrite: {
				  ['^' + process.env.VUE_APP_BASE_HELP_URL]: ''
				}
			},

      // 流式聊天接口代理
      '/stream-api': {
        target: 'https://u461063-b0dd-d6bab371.westc.gpuhub.com:8443',
        changeOrigin: true,
        secure: true,
        pathRewrite: {
          '^/stream-api': ''
        }
      },
		},
		disableHostCheck: true
	},
	configureWebpack: {
		name: name,
		resolve: {
			alias: {
				'@': resolve('src')
			}
		},
		plugins: [
			new webpack.ProvidePlugin({
				$: "jquery",
				jQuery: "jquery",
				jquery: "jquery",
				"windows.jQuery": "jquery"
			}),
			// 添加 BundleAnalyzerPlugin 插件
            ...(process.env.npm_config_report ? [new BundleAnalyzerPlugin()] : []),
		// 添加 Gzip 压缩插件
		new CompressionWebpackPlugin({
			filename: '[path][base].gz', // 输出文件名
			algorithm: 'gzip', // 使用 gzip 压缩
			test: /\.(js|css|html|svg)$/, // 匹配需要压缩的文件类型
			threshold: 10240, // 只有文件大小大于 10KB 才会被压缩
			minRatio: 0.8, // 压缩比例（压缩后体积 / 原始体积）
			deleteOriginalAssets: false // 是否删除原始文件，默认 false
		})

		],
		externals: {
			'./cptable': 'var cptable'
		},
	},
	chainWebpack(config) {
		config.plugins.delete('preload') // TODO: need test
		config.plugins.delete('prefetch') // TODO: need test

		// set svg-sprite-loader
		config.module
			.rule('svg')
			.exclude.add(resolve('src/assets/icons'))
			.end()
		config.module
			.rule('icons')
			.test(/\.svg$/)
			.include.add(resolve('src/assets/icons'))
			.end()
			.use('svg-sprite-loader')
			.loader('svg-sprite-loader')
			.options({
				symbolId: 'icon-[name]'
			})
			.end()

		config
			.when(process.env.NODE_ENV !== 'development',
				config => {
					config
						.plugin('ScriptExtHtmlWebpackPlugin')
						.after('html')
						.use('script-ext-html-webpack-plugin', [{
							// `runtime` must same as runtimeChunk name. default is `runtime`
							inline: /runtime\..*\.js$/
						}])
						.end()
					config
						.optimization.splitChunks({
							chunks: 'all',
							cacheGroups: {
								libs: {
									name: 'chunk-libs',
									test: /[\\/]node_modules[\\/]/,
									priority: 10,
									chunks: 'initial' // only package third parties that are initially dependent
								},
								elementUI: {
									name: 'chunk-elementUI', // split elementUI into a single package
									priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
									test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
								},
								commons: {
									name: 'chunk-commons',
									test: resolve('src/components'), // can customize your rules
									minChunks: 3, //  minimum common number
									priority: 5,
									reuseExistingChunk: true
								}
							}
						})
					config.optimization.runtimeChunk('single'), {
						from: path.resolve(__dirname, './public/robots.txt'), //防爬虫文件
						to: './', //到根目录下
					}
				}
			)
		config
			.entry('polyfill').add('@babel/polyfill') //兼容ie
	},
	transpileDependencies: ['element-ui', 'vuex'],
	/* scss引入变量---by zy 2022-06-24*/
	css: {
		loaderOptions: {
			scss: {
				additionalData: `@import "~@/assets/scss/zutai_base.scss";`
			}
		}
	}
}
