# 页面标题
VUE_APP_TITLE = 智库管理系统

# 开发环境配置
ENV = 'development'

# 智库管理系统/开发环境  //dev-api  prod-api
# 页面标题
VUE_APP_TITLE = 智库管理系统

# 开发环境配置
ENV = 'development'

# 智库管理系统/开发环境  //dev-api  prod-api
VUE_APP_BASE_API = '/prod-api'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true

# 包名
VUE_APP_PAKAGE_NAME = 'zutai'


VUE_APP_BASE_API = '/prod-api'
VUE_APP_BASE_AI = '/prod-ai'
VUE_APP_BASE_TSl = '/prod-translate'
VUE_APP_BASE_SQL = '/prod-SQL'
VUE_APP_BASE_GRAPH = '/prod-graph'
VUE_APP_BASE_HELP_URL = '/prod-help'
# VUE_APP_BASE_GATE_WAY_URL = 'http://127.0.0.1:18080'
# VUE_APP_BASE_GATE_WAY_URL = 'http://*************:18080'
# VUE_APP_BASE_GATE_WAY_URL = 'http://*************:18080'
VUE_APP_BASE_GATE_WAY_URL = 'http://*************:18080'
# VUE_APP_BASE_GATE_WAY_URL = 'http://************:18080'

# 网关地址 本机:http://localhost:8080 dev:http://*************:8080
# 线上dev:http://*************:30011  gateway.dev.com:30011
# 张强 http://*************:8080
# 罗华 http://*************:8080
# 家琪 http://*************:8080
# 李义 http://*************:8080
# 106:http://************:8080  // http://*************:8080
# 大佛寺现场 http://**********:8080
# 王蕾博
# GATE_WAY_URL = http://************:8080
# 测试
# GATE_WAY_URL = http://*************:8080
# 线上慎用！！！
# GATE_WAY_URL = http://*************:18080

