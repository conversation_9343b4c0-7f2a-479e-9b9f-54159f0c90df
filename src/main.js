import Vue from "vue";

import Cookies from "js-cookie";
import axios from "axios";
import formCreate from "@form-create/element-ui";
import FcDesigner from "@form-create/designer";
import Element from "element-ui";
import "./assets/styles/element-variables.scss";
// import 'element-ui/lib/theme-chalk/index.css'
import "@/core/http";
import JSONbig from "json-bigint";
import "@/components/Dialog";
//import '@/assets/style/index.scss';
import "@/core/mixins/global.js";
import TreeSelect from "@/components/TreeSelect";
import RichEditor from "@/components/RichEditor";
import InputNumberRange from "@/components/InputNumberRange";
import DateRange from "@/components/DateRange";
import FilterBox from "@/components/FilterBox";
import TableProgressColumn from "@/components/TableProgressColumn";
import VCharts from "v-charts";
import scroll from "vue-seamless-scroll";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
import plugins from './plugins' // plugins
import { codemirror } from 'vue-codemirror'
import 'codemirror/lib/codemirror.css'


import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';

import createMermaidPlugin from '@kangc/v-md-editor/lib/plugins/mermaid/cdn';
import '@kangc/v-md-editor/lib/plugins/mermaid/mermaid.css';

import Hljs from 'highlight.js';

// 导入剪贴板插件
import { ClipboardPlugin } from '@/utils/clipboard';

VMdPreview.use(githubTheme, {
  Hljs,
});
VMdPreview.use(createMermaidPlugin())

window.JSON = new JSONbig({ storeAsString: true });
Vue.component("tree-select", TreeSelect);
Vue.component("rich-editor", RichEditor);
Vue.component("input-number-range", InputNumberRange);
Vue.component("date-range", DateRange);
Vue.component("filter-box", FilterBox);
Vue.component("table-progress-column", TableProgressColumn);
Vue.use(VCharts);
Vue.use(scroll);
Vue.component("swiper", swiper);
Vue.component("swiper-slide", swiperSlide);
Vue.use(plugins)
Vue.use(VMdPreview);
Vue.use(ClipboardPlugin);

import "@/staticDict/onlineStaticDict.js";

import "@/assets/styles/index.scss"; // global css
import "@/assets/styles/coalmine.scss"; // coalmine css
import "@/assets/css/common.scss"; // 新公共
import App from "./App";
import store from "./store";
import router from "./router";
import directive from "./directive"; //directive
import { download } from "@/utils/request";
import FileSaver from "file-saver";
//import XLSX from "xlsx/dist/xlsx.mini.min.js"
// import "./assets/theme/font/font.css";
import "./assets/icons"; // icon
import "./permission"; // permission control
import moment from "moment";
import Storage from "vue-ls";

import VueTypedJs from "vue-typed-js";

Vue.use(VueTypedJs);
import voiceInputButton from "voice-input-button2";
Vue.use(voiceInputButton, {
  appId: "90300555", // 您申请的语音听写服务应用的ID
  apiKey: "9ba10256bd4a6cf56a40307a35731e3c", // 您开通的语音听写服务的 apiKey
  apiSecret: "YzJjN2IxODg5MDE3MjkwOTRmYzVmZmM3", // 您开通的语音听写服务的 apiSecret
  color: "#ccc", // 按钮图标的颜色
  tipPosition: "top", // 提示条位置
});
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree,
  dataComparison,
  bigLoadingShow,
  bigLoadingHide,
  systemRh,
  bytesToSize,
  checkImageExistsWithGet,
  getFileIcon,
} from "@/utils/coalmine";
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar";
// 富文本组件
import Editor from "@/components/Editor";
// 文件上传组件
import FileUpload from "@/components/FileUpload";
// 图片上传组件
import ImageUpload from "@/components/ImageUpload";
// 字典标签组件
import DictTag from "@/components/DictTag";
// 头部标签组件
import VueMeta from "vue-meta";
// 选择用户-单选
import selectUserRadio from "@/components/selectUserRadio";
// 选择list，显示数量，并可以删除
import selectNumDel from "@/components/selectNumDel";

import * as echarts from "echarts";
Vue.prototype.$echarts = echarts;

import "bpmn-js/dist/assets/diagram-js.css"; // 左边工具栏以及编辑节点的样式
import "bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css";
// 组态平台----start
import "./utils/utili/directives.js";
import "./utils/utili/drag.js";
import SSO from "./cas/sso";
import fcTreeSelect from "@/views/formCreat/components/component-elm-organizationSlect/index.vue";

// import "./utils/typed.js"
// console.log(new Typed() );
// websocket
import socket from "./utils/socket";
Vue.prototype.websocketUrl = socket;

import IntelligentAPI from "@/api/supervise/Intelligent/index.js";
Vue.prototype.IntelligentAPI = IntelligentAPI;

//挂载webSocket
var SocketService = [];
try {
  SocketService = require(`@/utils/SocketService`).default;
  Vue.prototype.$socket = SocketService.Instance;
} catch (e) {}
// 全局方法挂载
//系统编码
//先获取系统动态title
import { setToken } from "@/utils/auth";
import { getSystemConfig } from "@/api/login";
import logoImg from "@/assets/logo/logo.png";
const systemData = getSystemConfig().then((res) => {
  Vue.prototype.systemName = res.data.systemName || "智库管理系统"; //智库管理系统;
  for (let obj of document.querySelectorAll("link[rel='icon']")) {
    obj.remove();
  }
  var link = document.createElement("link");
  link.type = "image/png";
  link.rel = "icon";
  link.href = logoImg;

  new Promise(function (resolve, reject) {
    var ImgObj = new Image(); //判断图片是否存在
    ImgObj.src = res.data.systemPic;
    ImgObj.onload = function (_res) {
      link.href = res.data.systemPic;
      console.log(link.href);
    };
    ImgObj.onerror = function (err) {};
  }).catch((e) => {});

  document.getElementsByTagName("head")[0].appendChild(link);
  console.log(link, "#####");
  const url = window.location.search;
  const token = url.substring(url.lastIndexOf("=") + 1, url.length);
  console.log(token);
  if (token) {
    // setToken(token);
  }

  return res.data;
});

// const WS_API = getConfigKey("WS_API").then(res=>{
//   Vue.prototype.WS_API = res.msg
// })

Vue.prototype.systemNo = "";
Vue.prototype.SSO = SSO;
Vue.prototype.getDicts = getDicts;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;
Vue.prototype.dataComparison = dataComparison;
Vue.prototype.bigLoadingShow = bigLoadingShow;
Vue.prototype.bigLoadingHide = bigLoadingHide;
Vue.prototype.bytesToSize = bytesToSize;
Vue.prototype.checkImageExistsWithGet = checkImageExistsWithGet;
Vue.prototype.systemRh = systemRh;
Vue.prototype.FileSaver = FileSaver;
//Vue.prototype.XLSX = XLSX;
Vue.prototype.$axios = axios;
Vue.prototype.getFileIcon = getFileIcon;

Vue.prototype.msgSuccess = function (msg) {
  this.$message({ showClose: true, message: msg, type: "success" });
};

Vue.prototype.msgError = function (msg) {
  this.$message({ showClose: true, message: msg, type: "error" });
};

Vue.prototype.msgWarning = function (msg) {
  this.$message({ showClose: true, message: msg, type: "warning" });
};

Vue.prototype.msgInfo = function (msg) {
  this.$message.info(msg);
};

// 全局组件挂载
Vue.component("DictTag", DictTag);
Vue.component("Pagination", Pagination);
Vue.component("RightToolbar", RightToolbar);
Vue.component("Editor", Editor);
Vue.component("FileUpload", FileUpload);
Vue.component("ImageUpload", ImageUpload);
Vue.component("selectUserRadio", selectUserRadio);
Vue.component("selectNumDel", selectNumDel);
Vue.component("fc-treeSelect", fcTreeSelect);
formCreate.component("fc-treeSelect", fcTreeSelect);

const options = {
  namespace: "pro_", // key键前缀
  name: "ls", // 命名Vue变量.[ls]或this.[$ls],
  storage: "local", // 存储名称: session, local, memory
};

Vue.use(Storage, options);
Vue.use(directive);
Vue.use(VueMeta);
Vue.use(moment);
Vue.use(codemirror)
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */
Vue.use(Element, {
  size: Cookies.get("size") || "medium", // set element-ui default size
  zIndex: 900,
});
Vue.use(formCreate);
Vue.use(FcDesigner);

Vue.config.productionTip = false;

systemData.then((res) => {
  new Vue({
    el: "#app",
    router,
    store,
    render: (h) => h(App),
  });
});
