<template>
  <div class="app-container-chat" v-loading="loading">
    <div class="card-main">
      <div class="card-content">
        <template v-if="list && list.length">
          <div v-for="(item, index) in list" :key="index" class="card">
            <label class="checkboxcontainer">
              <input type="checkbox" v-model="item.checkboxStates" />
              <div class="checkmark"></div>
            </label>
            <div class="text">
              <img :src="getImg(item.userDocName)" alt="文件类型图标" />
              <div @click="previewFile(item)" class="filename">{{ item.userDocName }}</div>
            </div>
          </div>
        </template>

        <el-empty
          v-if="list && !list.length"
          description="暂无数据"
          style="width: 100%"
        />
      </div>
      <div class="card-btns">
        <Cusbtn
          v-for="button in computedBtns"
          :key="button.key"
          :btnText="button.name"
          @click.native="handleButtonClick(button.key)"
          :class="{ disableBtns: button.disableBtns }"
        />
      </div>
    </div>
    <pagination
      style="margin-right: 60px"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNum"
      :total="total"
      @pagination="fetchData"
      v-show="total > 0"
    />
    <!-- 文件上传 -->
    <UploadFile ref="uploadFileRef" @updataList="fetchData" />
    <!-- 文件对话 -->
    <FilesChat ref="filesChatRef" />
    <!-- 大纲对比 -->
    <DirectoryDiff ref="directoryDiffRef" />
    <!-- 文件对比 -->
    <FileDiff ref="fileDiffRef" />
    <!-- 单文件大纲提取 -->
    <SingleDirectory ref="singleDirectoryRef" />
  </div>
</template>

<script>
import Cusbtn from "./components/cusbtns.vue";
import UploadFile from "./components/uploadFile.vue";
import FilesChat from "./components/filesChat.vue";
import DirectoryDiff from "./components/directoryDiff.vue";
import SingleDirectory from "./components/singleDirectory.vue";
import FileDiff from "./components/fileDiff.vue";
import serviceKnowledge from "@/api/knowledge.js";
import userService from "@/api/techdocmanage/docCenter/user";
export default {
  name: "CardOperation",
  components: { Cusbtn, UploadFile, FilesChat, DirectoryDiff, FileDiff,SingleDirectory },
  data() {
    return {
      loading: false,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      list: [],
      folderInfo: null,
    };
  },
  computed: {
    computedBtns() {
      const selectedCount = this.list.filter(
        (item) => item.checkboxStates
      ).length;

      const buttons = [
            // 默认不禁用
            { name: "上传", key: 1, disableBtns: false },
            { name: "文档管理", key: 6, disableBtns: false },
            // 只有当 selectedCount 大于 0 时不禁用
            { name: "对话", key: 2, disableBtns: selectedCount <= 0 },
            // 只有当 selectedCount 等于 1 时不禁用
            { name: "智能核稿", key: 3, disableBtns: selectedCount !== 1 },
            { name: "提取大纲", key: 7, disableBtns: selectedCount !== 1 },
            // 只有当 selectedCount 等于 2 时不禁用
            { name: "大纲对比", key: 4, disableBtns: selectedCount !== 2 },
            { name: "文件对比", key: 5, disableBtns: selectedCount !== 2 },
          ];
      return buttons;
    },
  },
  created() {
    this.$nextTick(() => {
      this.getSpecifiedFolder();
    });
  },
  methods: {
    async getSpecifiedFolder() {
      const { code, data } = await serviceKnowledge.getSpecifiedFolder();
      if (code === 200) {
        this.folderInfo = data;
        this.fetchData();
      }
    },
    async fetchData() {
      console.log(this.list);
      this.loading = true;
      try {
        const response = await serviceKnowledge.getCardOperation({
          ...this.queryParams,
          userDocParent: this.folderInfo.id,
        });
        if (response.code === 200) {
          this.list = response.rows;
          this.total = response.total;
        }
      } catch (error) {
        console.error("数据获取失败:", error);
      } finally {
        this.loading = false;
      }
    },
    async handleButtonClick(buttonKey) {
      const selectedFiles = this.list.filter((item) => item.checkboxStates);
      console.log("Button clicked:", buttonKey, selectedFiles);

      if(buttonKey == 4 || buttonKey == 5 || buttonKey == 7){
      // 获取选中的文件名称+
        const selectedFileNames = selectedFiles.map(item => item.userDocName);
        // 提取文件名后缀
        const fileExtensions = selectedFileNames.map(fileName => fileName.split('.').pop());
        // 如果只包含doc和docx文件时，启用按钮
        if (!fileExtensions.every(ext => ['doc', 'docx'].includes(ext))) {
          this.$message.error("只支持doc/docx格式,请重新选择！");
          return;
        }
      }

 const checkLastVersionIdExists=(files)=> {
  return files.every(file => 'lastVersionId' in file && file.lastVersionId !== undefined && file.lastVersionId !== null);
}
      switch (buttonKey) {
        case 1:
          this.$refs.uploadFileRef.initData(this.folderInfo);
          break;
        case 2:
          this.$refs.filesChatRef.initData(selectedFiles);
          break;
        case 3:
          let params = {
            resultId: selectedFiles[0].id,
            docId: selectedFiles[0].docId,
          };
          const { code, data } = await userService.aiReviewDraft(params);
          if (code == 200) {
            this.$message.success("已产生修订记录");
            this.$router.push({
              path: "/techmanage/smartGenerate/formation",
            });
          }
          break;
        case 4:
          console.log("selectedFiles",!checkLastVersionIdExists(selectedFiles));
          if(!checkLastVersionIdExists(selectedFiles)){
            this.$message.error("请选择其他文件,该文件暂无版本,无法对比");
            return;
          }
          this.$refs.directoryDiffRef.initData(selectedFiles);
          break;
        case 5:
          if(!checkLastVersionIdExists(selectedFiles)){
            this.$message.error("请选择其他文件,该文件暂无版本,无法对比");
            return;
          }
          this.$refs.fileDiffRef.initData(selectedFiles);
          break;
        case 6:
            this.$router.push({
              path: "/techmanage/knowledgeManager/personalDocuments",
            });
          break;
        case 7:
          if(!checkLastVersionIdExists(selectedFiles)){
            this.$message.error("请选择其他文件,该文件暂无版本,无法对比");
            return;
          }
          this.$refs.singleDirectoryRef.initData(selectedFiles);
          break;
        default:
          break;
      }
    },
    // 获取图片地址
    getImg(filename) {
      const docExt = filename.split(".").at(-1).toLowerCase();
      const docExtImg = {
        doc: "type-word.png",
        docx: "type-word.png",
        pdf: "type-pdf.png",
        html: "type-html.png",
        txt: "type-txt.png",
        excel: "type-excel.png",
        cad: "type-cad.png",
        image: "type-image.png",
      };
      const imgPath = docExtImg[docExt] || "type-other.png";
      const img = require(`@/assets/stagingimage/${imgPath}`);
      return img;
    },
    // 文件预览
    previewFile(item){
      this.$router.push({
        name: "previewPdf",
      });
      this.$store.commit("common/PDFDOCID", item.docId);
    }
  },
};
</script>
<style scoped lang="scss">
.app-container-chat {
  // margin: 10px;
  padding: 10px;
  border-radius: 5px;
  background: #ffffff;
  height: calc(100vh - 112px);
  .card-main {
    display: flex;
  }
  .card-content {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 20px;
    height: 100%;
    .card {
      box-sizing: border-box;
      width: 360px;
      height: 100px;
      background: rgba(217, 217, 217, 0.58);
      border: 1px solid white;
      box-shadow: 12px 17px 51px rgba(0, 0, 0, 0.22);
      backdrop-filter: blur(6px);
      border-radius: 17px;
      text-align: center;
      cursor: pointer;
      transition: all 0.5s;
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;
      font-weight: bolder;
      color: black;
      .text {
        display: flex;
        align-items: center;
        .filename {
          width: 270px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 4; /* 显示3行 */
          -webkit-box-orient: vertical; /* 垂直排列子元素 */
        }
      }
    }

    .card:hover {
      border: 1px solid black;
      transform: scale(1.05);
    }

    .card:active {
      transform: scale(0.95) rotateZ(1.7deg);
    }
  }
  .card-btns {
    width: 100px;
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }
}

/* From Uiverse.io by elijahgummer */
/* Hide the default checkbox */
.checkboxcontainer input {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkboxcontainer {
  display: block;
  position: relative;
  cursor: pointer;
  font-size: 20px;
  user-select: none;
  border: 3px solid #beddd0;
  border-radius: 10px;
  overflow: hidden;
}

/* Create a custom checkbox */
.checkmark {
  position: relative;
  top: 0;
  left: 0;
  height: 1.3em;
  width: 1.3em;
  background-color: #2dc38c;
  border-bottom: 1.5px solid #2dc38c; /* Bottom stroke */
  box-shadow: 0 0 1px #cef1e4, inset 0 -2.5px 3px #62eab8,
    inset 0 3px 3px rgba(0, 0, 0, 0.34); /* Inner shadow */
  border-radius: 8px;
  transition: transform 0.3s ease-in-out; /* Transition for smooth animation */
}

/* When the checkbox is checked, modify the checkmark appearance */
.checkboxcontainer input:checked ~ .checkmark {
  transform: translateY(-40px); /* Move up */
  animation: wipeUp 0.6s ease-in-out forwards; /* Apply wipe animation */
}

/* When the checkbox is not checked, modify the checkmark appearance */
.checkboxcontainer input:not(:checked) ~ .checkmark {
  transform: translateY(40px); /* Move down */
  animation: wipeDown 0.6s ease-in-out forwards; /* Apply wipe animation */
}

/* Keyframes for wipe animations */
@keyframes wipeDown {
  0% {
    transform: translateY(0); /* Starting position */
  }
  100% {
    transform: translateY(40px); /* End position */
  }
}

@keyframes wipeUp {
  0% {
    transform: translateY(40); /* Starting position */
  }
  100% {
    transform: translateY(0px); /* End position */
  }
}

/* Create the checkmark/indicator */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.checkboxcontainer input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.checkboxcontainer .checkmark:before {
  content: "";
  position: absolute;
  left: 10px;
  top: 4px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  box-shadow: 0 4px 2px rgba(0, 0, 0, 0.34); /* Icon drop shadow */
}

::v-deep .disableBtns{
      pointer-events: none;
    /* background-color: #d0d5da; */
    cursor: not-allowed;
    opacity: 0.5;
}
</style>
