<template>
  <el-card class="box-card" shadow="always">
    <div slot="header" class="clearfix">
      <span>{{ title }}</span>
      <el-button-group style="float: right;">
        <el-button icon="el-icon-plus" round :loading="loadingOptions.loading" :disabled="loadingOptions.isDisabled" @click="submitForm">{{ loadingOptions.loadingText }}</el-button>
        <el-button icon="el-icon-back" round @click="showCard">返回</el-button>
      </el-button-group>
    </div>
    <div class="body-wrapper">
      <el-form ref="form" :model="form" :rules="rules" label-width="95px">
        <el-form-item label="数据源类型" prop="dbType">
          <el-select v-model="form.dbType">
            <el-option
              v-for="dict in dataDbTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据源名称" prop="sourceName">
          <el-input v-model="form.sourceName" placeholder="请输入数据源名称" />
        </el-form-item>
        <el-form-item label="主机" prop="host">
          <el-input v-model="form.dbSchema.host" placeholder="请输入主机" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="form.dbSchema.port" placeholder="请输入端口" />
        </el-form-item>
        <el-form-item v-if="form.dbType === '3' || form.dbType === '4'" label="服务名" prop="sid">
          <el-input v-model="form.dbSchema.sid" placeholder="请输入服务名" />
        </el-form-item>
        <el-form-item v-if="form.dbType !== '3' && form.dbType !== '4'" label="数据库" prop="dbName">
          <el-input v-model="form.dbSchema.dbName" placeholder="请输入数据库" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.dbSchema.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.dbSchema.password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status">
            <el-option
              v-for="dict in dataStatusOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item>
          <el-button  type="primary" @click="handleCheckConnection">连通性检测</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-card>
</template>

<script>
import { getDataSource, updateDataSource, checkConnection } from '@/api/metadata/datasource'

export default {
  name: 'DataSourceEdit',
  props: {
    data: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      title: '数据源编辑',
      // 展示切换
      showOptions: {
        data: {},
        showList: true,
        showAdd: false,
        showEdit: false,
        showDetail: false
      },
      // 保存按钮
      loadingOptions: {
        loading: false,
        loadingText: '保存',
        isDisabled: false
      },
      active: 1,
      // 表单参数
      form: {
        dbSchema: {
          host: undefined,
          port: undefined,
          dbName: undefined,
          username: undefined,
          password: undefined,
          sid: undefined
        },
      },
      // 表单校验
      rules: {
        dbType: [
          { required: true, message: '数据源类型不能为空', trigger: 'change' }
        ],
        sourceName: [
          { required: true, message: '数据源名称不能为空', trigger: 'blur' }
        ],
        'dbSchema.host': [
          { required: true, message: '主机不能为空', trigger: 'blur' }
        ],
        'dbSchema.port': [
          { required: true, message: '端口不能为空', trigger: 'blur' }
        ],
        'dbSchema.sid': [
          { required: true, message: '服务名不能为空', trigger: 'blur' }
        ],
        'dbSchema.dbName': [
          { required: true, message: '数据库不能为空', trigger: 'blur' }
        ],
        'dbSchema.username': [
          { required: true, message: '用户名不能为空', trigger: 'blur' }
        ],
        'dbSchema.password': [
          { required: true, message: '密码不能为空', trigger: 'blur' }
        ]
      },
      // form2: {
      //   host: undefined,
      //   port: undefined,
      //   dbName: undefined,
      //   username: undefined,
      //   password: undefined,
      //   sid: undefined
      // },
      dataStatusOptions:[],
      dataDbTypeOptions:[],
    }
  },
  mounted() {
    this.getDataSource(this.data.id)
  },

  created() {
    this.getDicts("sys_data_status").then(response => {
      this.dataStatusOptions = response.data;
    });
    this.getDicts("data_db_type").then(response => {
      this.dataDbTypeOptions = response.data;
    });
  },

  methods: {
    showCard() {
      this.$emit('showCard', this.showOptions)
    },
    /** 获取详情 */
    getDataSource: function(id) {
      getDataSource(id).then(response => {
        if(response.code == '200'){
          this.form = response.data
          // this.form2 = this.form.dbSchema
          }
      })
    },

    /** 检测数据库连通性 */
    handleCheckConnection() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // this.form.dbSchema = this.form2
          checkConnection(this.form).then(response => {
            if(response.code == '200'){
              this.$message.success('连接成功')
            }else{
              this.$message.success('连接失败')
            }


          })
        }
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // this.form.dbSchema = this.form2
          this.loadingOptions.loading = true
          this.loadingOptions.loadingText = '保存中...'
          this.loadingOptions.isDisabled = true
          updateDataSource(this.form).then(response => {
            if (response.code == '200') {
              this.$message.success('保存成功')
              setTimeout(() => {
                // 2秒后跳转列表页
                this.$emit('showCard', this.showOptions)
              }, 2000)
            } else {
              this.$message.error('保存失败')
              this.loadingOptions.loading = false
              this.loadingOptions.loadingText = '保存'
              this.loadingOptions.isDisabled = false
            }
          }).catch(() => {
            this.loadingOptions.loading = false
            this.loadingOptions.loadingText = '保存'
            this.loadingOptions.isDisabled = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-card ::v-deep .el-card__body {
  height: calc(100vh - 230px);
  overflow-y: auto;
}
</style>
