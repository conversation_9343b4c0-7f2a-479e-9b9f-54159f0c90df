<template>
  <div style="height: calc(100vh - 400px);overflow: auto;">
    <div class="cardList">
      <el-card class="box-card" v-for="(item,index) in cardList" :key="index">
        <div slot="header">
          <span>分类:{{ typeFn(item.typeIndex)}}</span>
        </div>
        <div class="content">
          <header class="mb10">
            <el-tooltip
              class="item"
              effect="dark"
              :content="item.docName"
              placement="top-start"
            >
              <div class="text">{{item.docName}}</div>
            </el-tooltip>
          </header>
          <div class="mb10 keyword">
            <span>关键词:</span>
            <template v-if="item.keywordsIndex">
              <span v-for="itemkeyword in item.keywordsIndex.split(',')" :key="itemkeyword">
                <el-tag class="ml10 tag-item">{{itemkeyword}}</el-tag>
              </span>
            </template>

          </div>
          <div class="mb10 keyword">
            <span>标签:</span>
            <template v-if="item.labelIndex">
              <span v-for="itemlabel in item.labelIndex.split(',')" :key="itemlabel">
                <el-tag class="ml10 tag-item">{{itemlabel}}</el-tag>
              </span>
            </template>
          </div>
        </div>
        <footer>
          <div style="font-size: 12px;"><span class="el-icon-user mr5"></span>{{item.createBy}}</div>
          <div class="preview-btn">
           <!--  <span class="el-icon-view"></span>
            <span  class="ml10">{{item.createId}}</span>
            <span class="el-icon-download ml10"></span>
            <span class="ml10">{{item.createDeptId}}</span> -->
            <!-- <span class="el-icon-eye-open ml10"></span> -->
            <svg-icon slot="suffix" icon-class="eye-open"/>
            <span class="ml5" style="cursor: pointer;" @click="filePreview(item)">预览</span>
          </div>
        </footer>
      </el-card>
    </div>
    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->
  </div>
</template>

<script>
export default {
  name: "",
  components: {},
  props: {
    cardList:{
      typeof:Array,
      default:()=>[]
    }
  },
  data() {
    return {
      total: 10,
      queryParams: {
        pageNum: 1,
        pageSize: 8,
      },
      sys_notice_type:[]
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
                this.getDicts("doc_business_type").then((response) => {
      this.sys_notice_type = response.data;
    });
  },
  methods: {
    getList() {},
        // 文件预览
    filePreview(row) {
      this.$router.push({
        path: "/techmanage/assistedWriting/tem-file?id=" + row.id,
      });
    },
        typeFn(row) {
      return this.selectDictLabel(this.sys_notice_type, row);
    },
  },
};
</script>

<style scoped lang="scss">
.cardList {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  justify-items: center;
  gap: 10px;
  .box-card {
    width: 100%;
    border-radius: 8px;
    &:hover{
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
      border-color: #bfdbfe !important;
    }
  }
  .content {
    margin: 10px auto;
    header {
      .text {
        width: 360px; /* 设置容器宽度 */
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 文本溢出容器时隐藏 */
        text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
        font-weight: 600;
        font-size: 15px;
        color: #000;
        &:hover{
          color: #1d4ed8;
          cursor: unset;
        }
      }
    }
    .keyword {
      width: 360px; /* 设置容器宽度 */
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 文本溢出容器时隐藏 */
      text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
    }
    .tag-item{
      border-radius: 30px;
      cursor: pointer;
      &:hover{
        background-color: #dbeafe;
        color: #1d4ed8;
      }
    }
  }
  footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .preview-btn{
    font-size: 12px;
    &:hover{
      color: #2563eb;
      cursor: pointer;
    }
  }
}
</style>
