<template>
  <div class="card">
    <header class="title" @click="cardDetail(item.docType)">
      <span></span>
      <span class="name">{{ item.docType }}({{ item.docCount }})</span>
      <i class="el-icon-document"></i>
    </header>
    <div class="content">
      <ul>
        <li
          v-for="(docItem, index) in item.docDocumentList"
          :key="index"
          @click="filePreview(docItem)"
          :title="docItem.docName"
        >
          <el-link class="text" type="info">{{ index + 1 }}.{{ docItem.docName }}</el-link>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  components: {},
  props: {
    item: {
      typeof: Object,
      default: () => {},
    },
    subjectName:{
      typeof:String,
      default:'0'
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 文件预览
    filePreview(row) {
      this.$router.push({
        path: "/techmanage/assistedWriting/tem-file?id=" + row.id,
      });
    },
    // 卡片详情
    cardDetail(val) {
      this.$router.push({
        path: "/CardDetail/Detail",
        query: {
          value: JSON.stringify({
            docType:val,
            subjectName:this.subjectName
          }),
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.card {
  box-sizing: border-box;
  width: 100%;
  min-width: 300px;
  height: 400px;
  //   background: rgba(217, 217, 217, 0.58);
  background: #fff;
  border: 1px solid white;
  box-shadow: 12px 17px 51px rgba(0, 0, 0, 0.22);
  backdrop-filter: blur(6px);
  border-radius: 17px;
  text-align: center;
  cursor: pointer;
  transition: all 0.5s;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  font-weight: bolder;
  color: black;
  display: flex;
  flex-direction: column;
  .title {
    height: 50px;
    line-height: 50px;
    width: 100%;
    background: #409eff;
    border-top-left-radius: 17px;
    border-top-right-radius: 17px;
    padding: 0 10px;
    color: #fff;
    .name {
      font-size: 16px;
      line-height: 16px;
    }
    .el-icon-document {
      float: right;
      margin-top: 20px;
    }
  }
  .content {
    flex: 1;
    width: 100%;
    overflow: hidden;
    ul {
      overflow: auto;
      height: 320px;
    }
    li {
      margin-bottom: 5px;
      text-align: left;
      padding: 0 10px;
      font-weight: 400;
      font-size: 14px;
      height: 28px;
      line-height: 22px;
      &::before{
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: #d1d5db;
        margin-right: 5px;
        margin-bottom: 2px;
        border-radius: 50%;
        &:hover{
          background-color: #409eff;
        }
      }
      &:hover::before{
        background-color: #409eff;
      }
    }
    &:hover{
      background-color: #f9fafb;
    }

  }
}
::v-deep .el-link--inner{
      color: #374151;
      width: 260px; /* 设置容器宽度 */
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 文本溢出容器时隐藏 */
      text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
}
.card:hover {
  //   border: 1px solid black;
  transform: scale(1.05);
}

.card:active {
  // transform: scale(0.95) rotateZ(1.7deg);
}
ul{
      padding: 0 !important;
}
</style>
