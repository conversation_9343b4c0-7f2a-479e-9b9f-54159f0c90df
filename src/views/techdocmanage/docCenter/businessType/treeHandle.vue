<template>
  <div>
    <!-- 新建文件夹 -->
    <el-dialog :title="isNew ? '新建文件夹' : '编辑文件夹'" :visible.sync="showNew" width="520px" @close="closeNewFolder">
      <el-form ref="newFolderForm" :model="newFolderForm" :rules="newFolderRules" label-width="100px">
        <el-form-item label="父级目录" v-if="leftCheckedNode">
          <el-input v-model="checkedNode.parent.data.treeName" disabled v-if="!isNew"></el-input>
          <el-input v-model="checkedNode.data.treeName" disabled v-if="isNew"></el-input>
        </el-form-item>
        <div style="margin-left: 100px;">
          <el-radio-group v-model="newFolderForm.createType" v-if="isNew">
            <el-radio :label="'customize'">普通</el-radio>
            <el-radio :label="'precut'">项目</el-radio>
          </el-radio-group>
        </div>
        <el-form-item label="文件夹名称" prop="folderName">
          <el-input v-model="newFolderForm.folderName"
                    :placeholder="newFolderForm.createType=='customize'? '请输入文件夹名称': '请输入项目名称'"
                    clearable></el-input>
        </el-form-item>
        <!--        <el-form-item label="文件夹名称" prop="folderId" v-if="newFolderForm.createType=='precut'" >-->
        <!--          <treeselect v-model="newFolderForm.folderId" :options="precutFolderOptions" :normalizer="normalizer" placeholder="请选择上级名称" />-->
        <!--        </el-form-item>-->
        <!-- <el-form-item label="知识库目录" v-if="isNew && leftCheckedNode? leftCheckedNode.type == 'business'? true: false: false" prop="path">
          <el-cascader clearable ref="cascader" @change="getCascaderClick" @visible-change="cascaderClick" placeholder="输入关键字进行过滤" :options="pTree" :props="cascaderProps" filterable class="cascader-width" v-model="newFolderForm.path">
          </el-cascader>
        </el-form-item> -->
        <div class="redHotHome"
             v-if="isNew && leftCheckedNode? leftCheckedNode.type == 'business'? true: false: false">
          <el-form-item class="redHot" key="pathVaild" prop="path" label="知识库目录">
            <el-cascader v-model="newFolderForm.path" @change="changeCascader"
                         @visible-change="cascaderClick" popper-class="time-loading" clearable ref="cascader"
                         placeholder="输入关键字进行过滤" :options="pTree" :props="cascaderProps1" class="cascader-width">
            </el-cascader>
          </el-form-item>
        </div>
        <el-form-item label="权限组织" prop="organization">
          <el-radio-group v-model="newFolderForm.organization">
            <el-radio :label="'department'">部门</el-radio>
            <el-radio :label="'post'">岗位</el-radio>
            <el-input placeholder="输入关键字进行过滤" v-model="filterText">
            </el-input>
          </el-radio-group>
          <el-tree class="filter-tree"
                   v-show="newFolderForm.organization == 'department'"
                   :data="deptOptionsTree"
                   :show-checkbox="true"
                   node-key="id"
                   :check-strictly="true"
                   empty-text="未设置部门权限数据"
                   :default-expand-all="true"
                   :default-expanded-keys="defaultExpanded"
                   :default-checked-keys="defaultChecked"
                   :filter-node-method="filterNode"
                   @check="getOrganizationTree" ref="tree">
          </el-tree>
          <el-tree class="filter-tree"
                   v-show="newFolderForm.organization == 'post'"
                   :data="postOptionsTree"
                   :show-checkbox="false"
                   node-key="id"
                   empty-text="未设置岗位权限数据"
                   :default-expand-all="true"
                   :default-expanded-keys="defaultExpanded"
                   :default-checked-keys="defaultCheckedPost"
                   :filter-node-method="filterNode"
                   @check="getOrganizationTree" ref="treePost">
                  <template #default="{ node, data }">
                    <span v-if="shouldShowCheckbox(data)">
                    <el-checkbox :checked="node.checked" :disabled="node.disabled" @change="(checked) => handleCheckChange(data, checked)"></el-checkbox>
                      {{node.label}}
                    </span>
                    <span v-else>
                     {{node.label}}
                     </span>
                  </template>
          </el-tree>
        </el-form-item>
        <el-form-item label="备注说明">
          <el-input type="textarea" :rows="4" v-model="newFolderForm.content" placeholder="请输入内容"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeNewFolder">取 消</el-button>
        <el-button type="primary" @click="addNewsFile">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 文件上传 -->
    <el-dialog title="文件上传" :visible.sync="showUpload" :close-on-click-modal="false" width="640px"
               @close="closeUpload">
      <el-form label-width="80px" ref="uploadForm" :model="uploadForm" :rules="uploadFormRules">
        <!-- <el-form-item label="密级" prop="grade">
          <el-radio-group v-model="uploadForm.grade">
            <el-radio :label="'A'">A级</el-radio>
            <el-radio :label="'B'">B级</el-radio>
            <el-radio :label="'C'">C级</el-radio>
            <el-radio :label="'D'">D级</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="文档目录" prop="catalog" v-if="
            leftCheckedNode
              ? leftCheckedNode.type == 'business'
                ? true
                : false
              : false
          ">
          <el-cascader @visible-change="cascaderClick" v-model="uploadForm.catalog" ref="cascader"
                       placeholder="输入关键字进行过滤" :options="pTree" :props="cascaderProps" filterable></el-cascader>
        </el-form-item>
        <el-form-item label="时间期限" prop="dateRange">
          <el-radio-group v-model="timeType">
            <el-radio :label="1">永久</el-radio>
            <el-radio :label="2">
              <el-date-picker v-model="uploadForm.dateRange" @input="$forceUpdate()"
                              :disabled="timeType!==2" type="datetimerange" align="center" unlink-panels
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                              value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptions">
              </el-date-picker>
            </el-radio>
          </el-radio-group>

        </el-form-item>
        <!-- <el-row>
                    <el-col :span="12">
                        <el-form-item label="生效时间" prop="startTime">
                            <el-date-picker v-model="uploadForm.startTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="起始时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="失效时间" prop="endTime">
                            <el-date-picker v-model="uploadForm.endTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="终止时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row> -->
        <el-form-item label="文件说明" prop="info">
          <el-input type="textarea" v-model="uploadForm.info"></el-input>
        </el-form-item>
        <el-form-item label="选择文件">
          <el-upload v-loading="loading" element-loading-text="正在上传中,请稍后"
                     element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)"
                     ref="upload" :auto-upload="false" multiple drag class="upload-demo" action="#"
                     :accept="acceptType" :limit="limit" :on-change="uploadChange" :before-remove="beforeRemove"
                     :on-remove="upLoadRemove" :on-preview="downLoadFile" :file-list="fileList"
                     :on-exceed="handleExceed">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              只能上传.docx,.doc,.xlsx,.xls,.pptx,.ppt,.pdf,.html,.txt,.csv,.svg,.png,.jpg格式的文件
            </div>
            <!-- <div class="el-upload__tip" slot="tip">
              <el-button
                style="margin-left: 10px"
                size="small"
                icon="el-icon-upload"
                type="success"
                @click="submitUpload"
                :disabled="fileList.length <= 0"
                >上传到服务器</el-button
              >
            </div> -->
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="checkUploadFile('zipUpload')">打包上传</el-button> -->
        <el-button @click="closeUpload">取 消</el-button>
        <el-button type="primary" @click="checkUploadFile" :disabled="fileList.length <= 0">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getToken
} from '@/utils/auth'
import userService from '@/api/techdocmanage/docCenter/user'
import {
  listFolderPrecut
} from "@/api/techdocmanage/precutFolder"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

export default {
  props: ['leftCheckedNodeId', 'leftCheckedNode', 'treedata', "checkedNode"],
  components: {
    Treeselect
  },
  data() {
    return {
      data: [],
      loading: false,
      disabled: true,
      // 上传文件地址
      uploadFileUrl: process.env.VUE_APP_BASE_API + '/filecore/upload',
      // 设置上传的请求头部
      headers: {
        Authorization: 'Bearer ' + getToken(),
      },
      // 上传的文件列表
      fileList: [],
      // 文件夹预制规则树选项
      precutFolderOptions: [],
      // 上传文件的限制
      limit: 50,
      // 上传数据
      uploadData: {},
      showNew: false,
      isNew: true,
      newFolderForm: {
        organization: 'department',
        folderName: '',
        content: '',
        createType: 'customize',
        // folderId: null,
      },
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'folderFlag',
      },
      cascaderProps: {
        // lazy: true,
        emitPath: false,
        checkStrictly: true,
        value: 'id',
        label: 'treeName',
        // leaf: 'childIsFolder',
      },
      cascaderProps1: {
        emitPath: false,
        checkStrictly: true,
        value: 'id',
        label: 'treeName',
        disabled: 'childIsFolder'
      },
      newFolderRules: {
        // 新建文件夹表单验证
        folderName: [{
          required: true,
          message: '请输入文件夹名称',
          trigger: 'change',
        },],
        // folderId: [
        //   {
        //     required: true,
        //     message: '请选择文件夹名称',
        //     trigger: 'change',
        //   },
        // ],
        path: [{
          required: false,
          message: '请选择目录',
          trigger: 'change',
        },],
        organization: [{
          required: true,
          message: '请选择权限部门',
          trigger: 'change',
        },],
      },
      showUpload: false,
      uploadForm: {
        // grade: "A",
      },
      uploadFormRules: {
        // 新建文件夹表单验证
        grade: [{
          required: true,
          message: '请选择密级',
          trigger: 'blur'
        },],
        catalog: [{
          required: true,
          message: '请选择目录',
          trigger: 'blur'
        },],
        fileList: [{
          required: true,
          message: '请选择文件',
          trigger: 'blur'
        },],
      },
      //业务目录树结构
      pTree: [],
      // 部门岗位树
      deptOptionsTree: [],
      postOptionsTree:[],
      // 岗位树
      optionsList: [],
      // 部门树
      deptTree: [],
      // 过滤文本
      filterText: '',
      // 权限机构id
      deptId: [],
      // 权限机构名称
      deptName: [],
      // 岗位部门区分标识
      deptFlag: true,
      // 默认展开节点
      defaultExpanded: [],
      // 默认选中节点
      defaultChecked: [],
      defaultCheckedPost:[],
      selectedDeptIds: [],
      acceptType: '.docx,.doc,.xlsx,.xls,.pptx,.ppt,.pdf,.html,.txt,.csv,.svg,.png,.jpg',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() <= new Date().setHours(0, 0, 0, 0) - 1
        },
        shortcuts: [{
          text: '一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          },
        },
          {
            text: '一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
      timeType: 1,
      nodeId: null,
    }
  },
  watch: {
    filterText(val) {
      if(this.newFolderForm.organization == 'department'){
        this.$refs.tree.filter(val)
      }else if(this.newFolderForm.organization == 'post'){
        this.$refs.treePost.filter(val)
      }
    },
    // newFolderForm: {
    //   handler(val) {
    //     if (val.organization === 'department') {
    //       this.deptOptionsTree = this.deptTree
    //       //
    //       this.deptFlag = true
    //     } else if (val.organization === 'post') {
    //       this.deptOptionsTree = this.optionsList
    //       //
    //       this.deptFlag = false
    //     }
    //     console.log("默认选中：", this.defaultChecked)
    //   },
    //   immediate: true,
    //   deep: true,
    // },
    'newFolderForm.organization': {
      handler(orgType) {
        if (orgType === 'department') {

        } else if (orgType === 'post') {
          console.log('切换岗位树：', this.optionsList , this.defaultChecked)
          let postId = this.$store.state.user.postIds
          console.log('岗位id：', postId)
          if (postId.length == 0){
            this.newFolderForm.organization = 'department';
            this.$message.error('当前用户未分配岗位！')
            return
          }
        }
      },
      immediate: true
    },

    leftCheckedNodeId: {
      handler(val) {
        // 部门树结构
        let id = {
          id: val,
        }
        this.nodeId = val;
        // this.deptTree = []
        // this.deptOptionsTree = []
        // this.defaultChecked = []
        // userService.deptTreeListNew(id).then((res) => {
        //   let data = res.data.map((org) => this.mapTree(org))
        //   this.deptTree = data
        //   this.deptOptionsTree = data
        //   console.log(data, ' this.deptOptionsTree')
        //   //
        // })
        // // 岗位树结构
        // userService.optionSelectNew(id).then((res) => {
        //   // this.optionsList = res.data
        //   this.optionsList = res.data.map((org) => this.mapTree1(org))
        //   console.log('过滤后的岗位：', this.optionsList)
        //   this.postOptionsTree = this.optionsList;
        //   //
        // })
      },
      deep: true,
    },
    timeType: {
      handler(val) {
        if (val == 1) {
          this.uploadForm.dateRange = []
        } else {

        }
      }
    },
  },
  mounted() {
  },
  created() {
    this.getTreeselect()
  },
  methods: {
    /** 查询文件夹预制规则下拉树结构 */
    getTreeselect() {
      listFolderPrecut().then(response => {
        this.precutFolderOptions = []
        this.precutFolderOptions = this.handleTree(response.data, "id", "pId")
      })
    },

    shouldShowCheckbox(data) {
      if(this.newFolderForm.organization === 'post' && data.type === '1'){
        return true;
      }
    },

    handleCheckChange(data, checked) {
      if (checked) {
        this.$refs.treePost.setChecked(data.id, true)
      } else {
        this.$refs.treePost.setChecked(data.id, false)
      }
    },

    /** 转换文件夹预制规则数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },

    handleNodeClick(data) {
      //
    },
    changeCascader(value) {
      this.$set(this.newFolderForm, 'path', value)
    },
    cascaderClick(val) {
      //
      // 文档目录树结构
      let data = {
        type: 'file',
      }
      // 获取文档目录树结构
      userService.treeUserList(data).then((res) => {
        // this.pTree = this.getTreeData(res.data)
        this.pTree = res.data.map((org) => this.getTreeData(org))
        //
      })
    },
    getCascaderClick(val) {
      this.selectCascaderId = val
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 新建文件夹- 获取点击文档目录树结构的数据
    getDirectoryTree(data) {
      this.fileDirectoryData = data
      this.fileDirectoryId = data.id
    },
    // 新建文件夹- 获取点击权限组织树结构的数据
    getOrganizationTree(data) {
      if (this.deptFlag) {
        this.deptId = []
        this.deptName = []
        this.$refs.tree.getCheckedNodes().forEach((element) => {
          this.deptId.push(element.id)
          this.deptName.push(element.label)
        })
      } else {
        // 岗位节点数组
        this.deptId = []
        this.deptName = []
        //岗位父级节点数组
        let arr2 = []
        this.$refs.tree.getCheckedNodes().filter((item) => {
          if (item.isLeaf === 0) {
            arr2.push(item)
          } else {
            this.deptId.push(item.id)
            this.deptName.push(item.label)
          }
        })
      }
    },
    /** 新建文件夹对话框：确定按钮 */
    addNewsFile() {
      this.loading = false;
      if (this.newFolderForm.directoryRange === '2') {
        this.getOrganizationTree()
      }
      this.$refs.newFolderForm.validate((valid) => {
        if (valid) {
          let checkedNodes = [];
          let deptId = [];
          let deptName = [];
          if(this.newFolderForm.organization === 'department'){
            checkedNodes = this.$refs.tree.getCheckedNodes();
             deptId = checkedNodes.map(item => item.id);
             deptName = checkedNodes.map(item => item.label);
          }else {
            checkedNodes = this.$refs.treePost.getCheckedNodes();
            deptId = checkedNodes
              .filter(item => item.type !== '0')
              .map(item => item.id);
            deptName = checkedNodes
              .filter(item =>item.type !== '0')
              .map(item => item.label);
          }
          console.log("当前选中的节点：",checkedNodes);
          console.log("当前选中的ID：",deptId);
          if (this.isNew) {
            let params = {
              resultName: this.newFolderForm.folderName,
              // folderId: this.newFolderForm.folderId,
              createFolderType: this.newFolderForm.createType,
              range: this.newFolderForm.organization,
              createBy: this.$store.state.user.nickName,
              deptId: deptId.toString(),
              deptName: deptName.toString(),
              description: this.newFolderForm.content,
              directoryRange: this.newFolderForm.directoryRange,
            }

            if (this.leftCheckedNode.type == 'business') {
              let cascaderValue = this.$refs.cascader.getCheckedNodes()[0]?.value
              if (cascaderValue) {
                params.fileDirectoryId = cascaderValue
              } else {
                return this.$message.warning('请选择知识库目录')
              }
            };
            (params.resultParent =
              this.leftCheckedNode.type == 'business' ?
                -1 :
                this.leftCheckedNodeId),
              (params.businessDirectoryId =
                this.leftCheckedNode.type == 'business' ?
                  this.leftCheckedNodeId :
                  this.leftCheckedNode.businessDirectoryId),
              console.log("新增参数：",params)
              userService.addBusinessDirectory(params).then((res) => {
                if (res.code == 200) {
                  this.$message.success('新增成功')
                  // console.log("展开：", this.expanKeys)
                  this.$emit('getTree')
                  this.$emit('getList', this.leftCheckedNode)
                  //this.loading = true;
                }
              })
            this.closeNewFolder()
          } else {
            let data = {
              id: this.leftCheckedNodeId,
              resultName: this.newFolderForm.folderName,
              operationType: 'rename',
              description: this.newFolderForm.content,
              range: this.newFolderForm.organization,
              deptId: deptId.toString(),
              deptName: deptName.toString(),
              directoryRange: this.newFolderForm.directoryRange,
              docType: 'directory',
            }
            userService
              .updateBusinessDirectory(data)
              .then((res) => {
                if (res.code == 200) {
                  this.$message.success('修改成功')
                  this.$emit('getTree')
                  this.closeNewFolder()
                }
              })

          }
        }
      })
    },
    // 新建文件夹对话框：打开对话框
    openFolder() {
      this.showNew = true
      this.newFolderForm.folderName = ""
      this.newFolderForm.organization = "department"


      this.deptTree = []
      this.deptOptionsTree = []
      this.defaultChecked = []
      let id = {
        id: this.nodeId,
      }
      userService.deptTreeListNew(id).then((res) => {
        let data = res.data.map((org) => this.mapTree(org))
        this.deptTree = data
        this.deptOptionsTree = data
        console.log(data, ' this.deptOptionsTree')
        if(this.deptOptionsTree.length === 0) {
          this.newFolderForm.organization = "post"
        }
      })
      // 岗位树结构
      userService.optionSelectNew(id).then((res) => {
        // this.optionsList = res.data
        this.optionsList = res.data.map((org) => this.mapTree1(org))
        console.log('过滤后的岗位：', this.optionsList)
        this.postOptionsTree = this.optionsList;
        if(this.postOptionsTree.length === 0) {
          this.newFolderForm.organization = "department"
        }
      })

      this.$nextTick(() => {
        let dataId = []
        let deptId = this.$store.state.user.deptId
        // this.deptTree.forEach((element) => {
        //   let deptId = this.$store.state.user.deptId
        //   console.log("当前登录人部门id", deptId);
        //   dataId.push(element.id, deptId)
        // })
        dataId.push(deptId)  // 默认选中当前登录人部门
        this.$refs.tree.setCheckedKeys(dataId)

        // 默认选中当前登录人岗位
        let postIds = []
        let postId = this.$store.state.user.postIds
        if (postId){
          postIds.push(postId)
          console.log('当前用户岗位id：', postIds)
          this.$refs.treePost.setCheckedKeys(postIds)
        }
      })
    },
    /** 新建文件夹对话框：关闭对话框 */
    closeNewFolder() {
      this.showNew = false
      this.isNew = true
      this.newFolderForm.organization = ''
      this.newFolderForm.content = ''
      this.deptOptionsTree = []
      this.$refs.newFolderForm.resetFields()
      // this.newFolderForm.folderId = null
    },
    /** 修改文件夹对话框：打开对话框事件 */
    editFolderFuc(e,node) {
      console.log("查看权限", e,node);
      if (e.type !== 'business') {
        // this.newFolderForm.organization = ""
        this.newFolderForm.folderName = e.resultName
        this.newFolderForm.content = e.description
        // this.$refs.newFolderForm.resetFields();
        this.showNew = true
        this.isNew = false
      } else if (!e) {
        this.$message.warning('请先选中一个文件夹')
        return
      } else {
        this.$message.warning('抱歉，此层级为目录，不能修改')
        return;
      }

      userService.deptTreeListNew({"id":node.parent.data.id}).then((res) => {
        let data = res.data.map((org) => this.mapTree(org))
        this.deptTree = data
        this.deptOptionsTree = data
        console.log(data, ' this.deptOptionsTree')
        //
      })
      // 岗位树结构
      userService.optionSelectNew({"id":node.parent.data.id}).then((res) => {
        console.log("岗位树结构", res)
        // this.optionsList = res.data
        this.optionsList = res.data.map((org) => this.mapTree1(org))
        this.postOptionsTree = this.optionsList
        //
      })
      this.showAuthFuc(e);
    },

    // 权限设定
    showAuthFuc(data) {
      console.log("权限设定", data);
      let fileState = data.fileStatus == '4' ? true : false;
      if (fileState) {
        this.$message.info('选中的文件已作废，无法进行操作!')
        return
      } else {
        let id = data.id
        userService.singleFileTree(id).then((res) => {
          console.log("权限结果：", res)
          if (res.code === 200) {
            let dataId = []
            this.newFolderForm.organization = res.data[id][0].authRange
            res.data[id].forEach((element) => {
                console.log("部门权限：", element.authId)
                dataId.push(element.authId);
            })
            if( this.newFolderForm.organization === 'department'){
              this.$refs.tree.setCheckedKeys(dataId)
              this.$refs.treePost.setCheckedKeys(this.$store.state.user.postIds)
            }else {
              console.log("岗位权限：", dataId)
              this.$refs.treePost.setCheckedKeys(dataId)
              this.$refs.tree.setCheckedKeys([this.$store.state.user.deptId])
            }
          }
        })
        // this.newFolderForm.ids = ids
        // this.newFolderForm.content = data.description
      }
    },

    /** 上传文件对话框：确定按钮 */
    checkUploadFile(uploadType) {
      this.$refs.uploadForm.validate((valid) => {
        if (valid) {
          this.uploadFile(uploadType)
        }
      })
    },
    async uploadFile(uploadType) {
      let flag = await this.submitUpload(uploadType)
      //
      if (flag) {
        let data
        this.$refs.uploadForm.validate((valid) => {
          if (valid) {
            let docIds = []
            let resultNames = []
            this.uploadData.forEach((element) => {
              docIds.push(element.id)
              resultNames.push(element.docName)
            })
            if (this.leftCheckedNode.type === 'business') {
              let cascaderValue =
                this.$refs.cascader.getCheckedNodes()[0].value
              // 目录级别上传
              data = {
                // 文档根节点
                fileDirectoryId: cascaderValue,
                // 父级ID
                // resultParent: this.leftCheckedNode.resultParent,
                // 密级
                secret: this.uploadForm.grade,
                // 开始时间
                // effectiveTime: this.timeType==1? '':this.uploadForm.dateRange[0],
                // 结束时间
                // invalidTime:this.timeType==1? '': this.uploadForm.dateRange[1],
                // 业务根节点
                businessDirectoryId: this.leftCheckedNode.id,
                // 文件版本
                edition: this.uploadData.edition,
                // 文件ID
                docIds: docIds,
                // 文件名称
                resultNames: resultNames,
                // 文件code
                uniqueCode: this.uploadData.docCode,
                description: this.uploadForm.info,
              }
            } else {
              // let cascaderValue = this.$refs.cascader.getCheckedNodes()[0].value;
              // 文件夹上传---文档根节点-父级ID-文件ID
              data = {
                // 文档根节点
                // fileDirectoryId: cascaderValue,
                // 父级ID
                resultParent: this.leftCheckedNode.id,
                // 密级
                secret: this.uploadForm.grade,
                // 开始时间
                // effectiveTime: this.timeType==1? '':this.uploadForm.dateRange[0],
                // 结束时间
                // invalidTime:this.timeType==1? '': this.uploadForm.dateRange[1],
                // 业务根节点
                // businessDirectoryId: this.leftCheckedNode.id,
                // 文件版本
                edition: this.uploadData.edition,
                // 文件ID
                docIds: docIds,
                // 文件名称
                resultNames: resultNames,
                // 文件code
                uniqueCode: this.uploadData.docCode,
                description: this.uploadForm.info,
              }
            }
            if (this.uploadForm.dateRange) {
              data = {
                ...data,
                effectiveTime: this.timeType == 1 ? '' : this.uploadForm.dateRange[0],
                invalidTime: this.timeType == 1 ? '' : this.uploadForm.dateRange[1],
              }
            }
            userService.addFile(data).then((res) => {
              if (res.code === 200) {
                this.disabled = true
                this.$emit('getList', this.leftCheckedNode)
              }
            })
            this.closeUpload()
          }
        })
      }
    },
    /** 上传文件对话框：关闭对话框 */
    closeUpload() {
      this.showUpload = false
      this.fileList = []
      this.$refs.uploadForm.resetFields()
      this.$refs.upload.clearFiles()
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}?`)
    },
    // 移除附件
    upLoadRemove(file, fileList) {
      let tempFileList = []
      for (var index = 0; index < this.fileList.length; index++) {
        if (this.fileList[index].name !== file.name) {
          tempFileList.push(this.fileList[index])
        }
      }
      this.fileList = tempFileList
    },
    // 监控上传文件列表
    uploadChange(file, fileList) {
      // 调用接口判断文件是否存在
      userService.checkFileUnique({
        "resultName": file.name,
        "businessDirectoryId": this.checkedNode.data.businessDirectoryId
      }).then((res) => {
        if (res.code === 200) {
          if (res.data) {
            this.$message.error(file.name + '文件已经存在!')
            fileList.pop()
          }
        }
      });
      const fileSuffix = file.name.substring(
        file.name.lastIndexOf('.') + 1
      )
      const whiteList = [
        'doc',
        'docx',
        'pdf',
        'html',
        'txt',
        'xls',
        'xlsx',
        'csv',
        'tsv',
        'ppt',
        'pptx',
        'svg',
        'png',
        'jpg',
        'tif',
        'gif',
        'bmp',
        'dwg',
        'dws',
        'dwt',
        'dxf',
        'zip'
      ]
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.error(
          '只能上传doc,docx,pdf,html,txt,xls,xlsx,csv,tsv,ppt,pptx,svg,png,jpg,tif,gif,bmp,dwg,dws,dwt,dxf,zip此类型的文件'
        )
        this.fileList = []
        return false
      }
      let existFile = fileList
        .slice(0, fileList.length - 1)
        .find((f) => f.name === file.name)
      if (existFile) {
        this.$message.error('当前文件已经存在!')
        fileList.pop()
      }
      this.fileList = fileList
      //
    },
    // 上传到服务器
    submitUpload(uploadType) {

      this.loading = true
      let formData = new FormData()
      //
      this.fileList.forEach((item) => {
        formData.append('file', item.raw)
      })
      formData.append('docParent', this.leftCheckedNode.docId)
      formData.append('docMount', 1)
      formData.append('edition', 'v1.0')
      let flag;
      if (uploadType == 'zipUpload') {
        let parentNode = {
          createBy: this.leftCheckedNode.createBy,
          createFolderType: this.leftCheckedNode.createFolderType,
          folderId: this.leftCheckedNode.folderId,
          range: this.leftCheckedNode.range,
          resultName: this.leftCheckedNode.resultName,
          resultParent: this.leftCheckedNode.id,
          businessDirectoryId: this.leftCheckedNode.businessDirectoryId,
        }
        Object.keys(parentNode).forEach(item => {
          formData.append(item, parentNode[item])
        })
        flag = userService.uploadByZip(formData).then((res) => {
          //
          if (res.code === 200) {
            this.uploadData = res.data
            this.loading = false
            this.disabled = false
            // this.$message.success('上传成功')
            return true
          } else {
            this.$message.error('上传失败, 请重试')
          }
        })
      } else {
        flag = userService.uploadFile(formData).then((res) => {
          //
          if (res.code === 200) {
            this.uploadData = res.data
            this.loading = false
            this.disabled = false
            // this.$message.success('上传成功')
            return true
          } else {
            this.$message.error('上传失败, 请重试')
          }
        })
      }
      return flag
    },
    // 点击文件进行下载
    downLoadFile(file) {
      var a = document.createElement('a')
      var event = new MouseEvent('click')
      a.download = file.name
      a.href = file.url
      a.dispatchEvent(event)
    },
    // 选取文件超过数量提示
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 ${limit} 个文件，本次选择了 ${files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    },
    // 递归遍历树形结构
    getTreeData(org) {
      // for (var i = 0; i < data.length; i++) {
      //   for (var j = 0; j < data[i].children.length; j++) {
      //     if (!data[i].children[j].childIsFolder) {
      //       // childIsFolder若为false，则将children设为null
      //       data[i].children[j].children = null
      //     } else {
      //       // children若不为空数组，则继续 递归调用 本方法
      //       this.getTreeData(data[i].children[j])
      //     }
      //   }
      // }
      // return data
      // org.childIsFolder 表示children为文件夹 将children置null
      const haveChildren =
        Array.isArray(org.children) && org.children.length > 0
      return {
        //分别将我们查询出来的值做出改变他的key
        createBy: org.createBy,
        createDeptBy: org.createDeptBy,
        createDeptId: org.createDeptId,
        createTime: org.createTime,
        directoryLevel: org.directoryLevel,
        directoryName: org.directoryName,
        directoryParent: org.directoryParent,
        directoryType: org.directoryType,
        fullName: org.fullName,
        fullPath: org.fullPath,
        id: org.id,
        treeName: org.treeName,
        type: org.type,
        //判断它是否存在子集，若果存在就进行再次进行遍历操作，直到不存在子集便对其他的元素进行操作
        children: haveChildren && !org.childIsFolder ?
          org.children.map((i) => this.getTreeData(i)) :
          null,
      }
    },
    // 更改所属部门为禁用
    mapTree(org) {
      let deptId = this.$store.state.user.deptId
      const haveChildren =
        Array.isArray(org.children) && org.children.length > 0
      return {
        //分别将我们查询出来的值做出改变他的key
        label: org.label,
        disabled: Boolean(deptId == org.id),
        id: org.id,
        isLeaf: org.isLeaf,
        //判断它是否存在子集，若果存在就进行再次进行遍历操作，直到不存在子集便对其他的元素进行操作
        children: haveChildren ?
          org.children.map((i) => this.mapTree(i)) :
          null,
      }
    },
    // 更改所属部门为禁用
    mapTree1(org) {
      // 如果 type 为 0 且 children 为空，则返回 null 表示过滤掉该节点
      if (org.type === '0' && (org.children === null || org.children.length === 0) ) {
        return null;
      }
      let postIds = this.$store.state.user.postIds;
      const haveChildren = Array.isArray(org.children) && org.children.length > 0;

      const node = {
        label: org.isLeaf === 0 ? org.label + '(部门)' : (org.isLeaf === 1 ? org.label + '(岗位)' : org.label + '(人员)'),
        disabled: Boolean(postIds.includes(org.id)),
        isLeaf: org.isLeaf,
        type: org.type,
        id: org.id,
        children: haveChildren ? org.children.map((i) => this.mapTree1(i)).filter(Boolean) : null,
      };
      return node;
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-upload-list {
  max-height: 115px;
  overflow-y: auto;
}

::v-deep .el-dialog__body {
  padding: 30px 20px 0;
}

.cascader-width {
  width: 373px;
}

.redHotHome {
  position: relative;

  .redHot {
    &::before {
      content: '*';
      color: #ff4949;
      margin-right: 4px;
      position: absolute;
      left: 2%;
      top: 35%;
    }
  }
}
</style>
