<template>
  <div class="table-wrap">
    <el-table
      ref="fileTable"
      :data="tableData"
      :row-style="{ height: '52px' }"
      :show-header="!isCardPlay"
      height="100%"
      :max-height="getTableHeight"
      tooltip-effect="dark"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @cell-mouse-enter="showOperation"
      @cell-mouse-leave="hiddenOperation"
	  v-if="tableView"
    >
      <el-table-column type="selection" width="42" v-if="selectLeftType!='0'"> </el-table-column>
      <el-table-column label="文件名称" prop="refresh" min-width="550">
        <template slot-scope="scope">
          <div
            v-show="!isCardPlay"
            @click="clickFilename(scope.row)"
            class="file-content"
          >
              <template v-if="scope.row.docType === 'directory'">
                <img
                  src="@/assets/docCenter/dir.png"
                  style="margin-right: 5px; flex-shrink: 0; width: 16px; height: 16px;"
                />
              </template>
              <template v-else>
                <img
                  :src="getFileIcon(scope.row)"
                  style="margin-right: 5px; flex-shrink: 0; width: 16px; height: 16px;"
                />
              </template>
            <div class="file-name-wrapper">
              <el-tooltip
                :content="scope.row.resultName"
                placement="bottom-start"
                effect="light"
              >
                <div class="file-name-text">{{
                  scope.row.resultName
                }}</div>
              </el-tooltip>
            </div>
          </div>
          <div
            v-show="!isCardPlay && scope.row.id === operationShowFileId"
            class="file-operation-content"
          >
            <el-tooltip
              v-show="item.label !== '编辑' && item.label !== '签出'"
              class="item"
              effect="light"
              :content="item.label"
              placement="top"
              v-for="(item, index) in btns"
              :key="index"
            >
              <el-button
                size="mini"
                :type="item.type"
                :icon="item.icon"
                circle
                v-hasPermi="item.permi"
                :class="item.disabled ? 'permiHidden' : 'permiShow'"
                @click="filetableHandle(item.value, scope.row)"
              ></el-button>
            </el-tooltip>
            <!-- <el-tooltip v-show="btns.length == 10 ? true : false" effect="dark" :content="scope.row.locked == 1 || scope.row.locked == null? item.label: item.label2" placement="top" v-for="item in btnsList" :key="item.id" v-hasPermi="item.permi" :class="item.disabled ? 'permiHidden' : 'permiShow'">
              <el-button size="mini" :type="item.type" :icon="scope.row.locked == 1 || scope.row.locked == null?item.icon:item.icon2" circle v-hasPermi="item.permi" :class="item.disabled ? 'permiHidden' : 'permiShow'" @click="filetableHandle(item.value, scope.row)"></el-button>
            </el-tooltip> -->
          </div>
          <div v-show="isCardPlay" class="file-content-card">
            <img
              v-show="scope.row.docType === 'directory'"
              src="@/assets/docCenter/dir.png"
            />
            <img
              v-show="scope.row.docType === 'file'"
              :src="getFileIcon(scope.row)"
            />
            <div class="card-style">
              <span class="card-style__filename">{{ scope.row.filename }}</span>
              <el-row style="display: flex;align-items: center;">
                <el-col
                  :span="showTools ? 11 : 12"
                  @click.native="clickFilename(scope.row)"
                >
                  <div class="file-name-wrapper">
                    <el-tooltip
                      :content="
                        scope.row.userDocName
                          ? scope.row.userDocName
                          : scope.row.resultName
                          ? scope.row.resultName
                          : scope.row.docName
						  ?scope.row.taskName
						  :scope.row.taskName
                      "
                      placement="bottom-start"
                      effect="light"
                    >
                      <div class="file-name-text">{{
                        scope.row.userDocName
                          ? scope.row.userDocName
                          : scope.row.resultName
                          ? scope.row.resultName
                          : scope.row.docName
						  ?scope.row.taskName
						  :scope.row.taskName
                      }}</div>
                    </el-tooltip>
                  </div>
                </el-col>
                <el-col :span="showTools ? 4 : 12">
                  <div class="card-info-text">
                    {{
                      collectEvent == 4 ? scope.row.deleteBy : scope.row.createBy
                    }}
                  </div>
                </el-col>
                <el-col :span="showTools ? 6 : 12">
                  <div class="card-info-text">
                    {{
                      collectEvent == 4
                        ? scope.row.deleteTime
                        : scope.row.createTime
                    }}
                  </div>
                </el-col>
                <el-col :span="3" v-show="showTools && scope.row.docType === 'file'">
                  <div style="display: flex;flex-direction: column;" >
                    <el-tag :type="getStateTagType(scope.row.fileStatus)" size="mini" class="file-status-tag">
                      {{ getState(scope.row.fileStatus) }}
                    </el-tag>
                    <span v-if="scope.row.convertVectorStatus"
                           style="margin-top: 4px;font-size: 10px;">
                      向量：{{ getVectorState(scope.row.convertVectorStatus) }}
                    </span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isCardPlay||selectLeftType=='0'"
        label="文件状态"
        width="250"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="table-cell-content">
			  <span v-if="selectLeftType=='0'">{{scope.row.taskStatus}}</span>
            <el-tag v-else :type="getStateTagType(scope.row.fileStatus)" size="small">
              {{ getState(scope.row.fileStatus) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isCardPlay"
        :label="category"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="table-cell-content">
            {{ collectEvent == 4 ? scope.row.deleteBy : scope.row.createBy }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        :label="isCardPlay ? '操作' : timeCategory"
        :width="isCardPlay ? (getMoreButtons().length > 0 ? 220 : 180) : 180"
      >
        <template slot-scope="scope">
          <div v-if="isCardPlay" class="handle-icon-line">
            <div v-show="scope.row.docType === 'file'||selectLeftType=='0'">
              <div class="default-buttons">
              <!-- 默认显示的按钮：下载、分享、删除、智能修订 -->
              <template v-for="(item, index) in getDefaultButtons(scope.row)">
                <el-tooltip
                  effect="dark"
                  :content="item.label"
                  placement="top"
                  v-if="checkPermi(item.permi)"
                  :class="item.disabled ? 'permiHidden' : 'permiShow'"
                  :key="`default-${index}`"
                >
                  <img
                    :src="scope.row.flag === '1' ? item.src2 : item.src"
                    class="handle-icon"
                    :class="item.disabled ? 'permiHidden' : 'permiShow'"
                    @click="filetableHandle(item.value, scope.row)"
                  />
                </el-tooltip>
              </template>
            </div>

            <!-- 更多操作下拉菜单 -->
            <el-dropdown
              v-if="getMoreButtons().length > 0"
              trigger="click"
              @command="(command) => handleDropdownCommand(command, scope.row)"
              class="more-dropdown"
            >
              <span class="more-btn">
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <template v-for="(item, index) in authMoreButtonsFilter(scope.row, getMoreButtons())">
                  <el-dropdown-item
                    :command="item.value"
                    v-if="checkPermi(item.permi)"
                    :disabled="item.disabled"
                    :key="`more-${index}`"
                  >
                    <i :class="item.icon" style="margin-right: 8px;"></i>
                    {{ item.label }}
                  </el-dropdown-item>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
            </div>

            <div v-show="scope.row.docType !== 'file'&&selectLeftType!='0'">
              <div class="default-buttons">
              <!-- 默认显示的按钮：下载、分享、删除、智能修订 -->
              <template v-for="(item, index) in getDirButtons()">
                <el-tooltip
                  effect="dark"
                  :content="item.label"
                  placement="top"
                  v-if="checkPermi(item.permi)"
                  :class="item.disabled ? 'permiHidden' : 'permiShow'"
                  :key="`default-${index}`"
                >
                  <img
                    :src="scope.row.flag === '1' ? item.src2 : item.src"
                    class="handle-icon"
                    :class="item.disabled ? 'permiHidden' : 'permiShow'"
                    @click="filetableHandle(item.value, scope.row)"
                  />
                </el-tooltip>
              </template>
            </div>
            </div>

          </div>
          <div v-else class="table-cell-content">
            {{
              collectEvent == 4 ? scope.row.deleteTime : scope.row.createTime
            }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 移动文件Dialog -->
    <el-dialog
      title="移动到"
      :visible.sync="showMove"
      width="520px"
      :close-on-click-modal="false"
    >
      <div class="tree-wrap">
        <el-tree
          v-if="showMove"
          ref="moveTree"
          empty-text="暂无文件夹数据"
          :data="newTreeData"
          @node-click="handleNodeClick"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span style="pointer-events: none">
              <i
                :class="
                  data.type === 'directory' || data.type === 'business'
                    ? 'el-icon-folder-opened'
                    : 'el-icon-document '
                "
                style="margin-right: 5px"
              />
              {{
                `${node.label}(${
                  data.type == "directory" || node.disabled
                    ? "可移动"
                    : "不可移动"
                })`
              }}
            </span>
          </span>
        </el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showMove = false">取 消</el-button>
        <el-button type="primary" :disabled="!isMove" @click="submitMove"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- 签入签出---start -->
    <el-dialog
      title="签出"
      :visible.sync="checkOutIn"
      width="520px"
      :close-on-click-modal="false"
    >
      <div>
        <h3 style="font-weight: 900">请启动桌面助手来完成此操作</h3>
        <h4>桌面助手可增强浏览器的功能，如可靠上传、文档编辑等</h4>
        <div class="checkOutIn">
          <div class="check">
            <div>已安装桌面助手</div>
            <el-button @click="oneTouchStart">一键启动</el-button>
            <div>可以在菜单中找到并点击启动桌面助手</div>
          </div>
          <el-divider direction="vertical"></el-divider>
          <div class="check">
            <div>如果没有下载请点击下方下载按钮</div>
            <el-button @click="dowloadClick">下载安装</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 原文件下载和pdf下载选择框 -->
    <el-dialog center title="下载" :visible.sync="visible" width="20%">
      <div style="display: flex; justify-content: center">
        <el-button type="success" size="mini" @click="downloadType(0)"
          >原文件下载</el-button
        >
        <!-- <el-button type="primary" size="mini" @click="downloadType(1)">pdf下载</el-button> -->
      </div>
    </el-dialog>
    <!-- 签入签出---end -->
    <detail-view ref="detailView"></detail-view>
    <share-view ref="shareView" @shareSet="shareSet"></share-view>
    <table-handle
      ref="tableHandle"
      :leftCheckedNodeId="leftCheckedNodeId"
      :leftCheckedNode="leftCheckedNode"
      v-bind="$attrs"
      v-on="$listeners"
    ></table-handle>
    <modify-file
      ref="modifyFile"
      v-bind="$attrs"
      v-on="$listeners"
      :leftCheckedNode="leftCheckedNode"
    ></modify-file>
    <!-- 标签设置组件-->
    <LabelSet ref="labelOpen" @getListChild="getListChild"></LabelSet>
    <!-- 权限设定 -->
    <el-dialog
      title="查看权限"
      :visible.sync="showAuth"
      width="520px"
      @close="closeAuth"
    >
      <el-form label-width="84px" :form="authForm">
        <el-form-item label="权限组织" prop="range">
          <el-radio-group v-model="authForm.range" disabled>
            <el-radio :label="'department'">部门</el-radio>
            <el-radio :label="'post'">岗位</el-radio>
          </el-radio-group>
          <el-tree class="filter-tree"
                   v-show="authForm.range == 'department'"
                   :data="deptOptionsTree"
                   :show-checkbox="true"
                   node-key="id"
                   :check-strictly="true"
                   empty-text="未设置部门权限数据"
                   :default-expand-all="true"
                   :default-expanded-keys="defaultExpanded"
                   :default-checked-keys="defaultChecked"
                   :filter-node-method="filterNode"
                   @check="getOrganizationTree" ref="tree">
          </el-tree>
          <el-tree class="filter-tree"
                   v-show="authForm.range == 'post'"
                   :data="postOptionsTree"
                   :show-checkbox="false"
                   node-key="id"
                   empty-text="未设置岗位权限数据"
                   :default-expand-all="true"
                   :default-expanded-keys="defaultExpanded"
                   :default-checked-keys="defaultCheckedPost"
                   :filter-node-method="filterNode"
                   @check="getOrganizationTree" ref="treePost">
            <template #default="{ node, data }">
                    <span v-if="shouldShowCheckbox(data)">
                    <el-checkbox :checked="node.checked" :disabled="node.disabled" @change="(checked) => handleCheckChange(data, checked)"></el-checkbox>
                      {{node.label}}
                    </span>
              <span v-else>
                     {{node.label}}
                     </span>
            </template>
          </el-tree>
        </el-form-item>
        <!-- <el-form-item label="备注说明" prop="content">
          <el-input type="textarea" :rows="4" v-model="authForm.content" placeholder="请输入内容" />
        </el-form-item> -->
      </el-form>
    </el-dialog>
	<el-dialog
	  title="分享"
	  :visible.sync="shareDocVisible"
	  width="650px"
	>
	  <el-form label-width="auto">
	    <el-form-item label="地址:">
		  <a :href="shareDocAddress" target="_blank" style="text-decoration: underline;color: var(--current-color);">{{shareDocAddress}}</a>
	    </el-form-item>
	  </el-form>
	</el-dialog>
  </div>
</template>

<script>
/**
 * 表格类型，不同类型表示不同的按钮组
 * 0——文件——收藏0、下载1、分享2、移动3、签出4、删除5、详细信息6
 * 1———回收站——还原0、彻底删除1
 */
import LabelSet from "../labelSet.vue";
import { getUserProfile } from "@/api/system/user";
import userService from "@/api/techdocmanage/docCenter/user";
import detailView from "../detailView.vue";
import shareView from "../shareView.vue";
import tableHandle from "./tableHandle.vue";
import modifyFile from "../modifyFile.vue";
import { getConfigKey } from "@/api/system/config";
import { checkPermi } from "@/utils/permission";
import { addShareDoc } from "@/api/techdocmanage/docUploadTask";
export default {
  name: "FileTable",
  components: {
    tableHandle,
    detailView,
    shareView,
    modifyFile,
    LabelSet,
  },
  props: [
    "tableData",
    "isCardPlay",
    "showTools",
    "btns",
    "treeData",
    "leftCheckedNode",
    "leftCheckedNodeId",
    "collectEvent",
	"selectLeftType"
  ],
  data() {
    return {
      isMove: true,
      newTreeData: [],
      downloadData: {},
      visible: false,
      // 初始化时获取当前打开页面的高度
      screenHeight: document.body.clientHeight,
      // 签入签出
      checkOutIn: false,
      statusclickTr: true,
      flag: "0",
      operationShowFileId: "", //
      showMove: false, //
      // defaultProps: {
      //   children: 'children',
      //   label: 'treeName',
      //   isLeaf: 'childIsFolder',
      // },
      // 文件移动
      fileMove: [],
      // 文件移动id
      fileMoveId: "",
      // 获取签入签出点击事件
      checkData: {},
      errorSetInerval: "",
      num: 0,
      // socket参数
      socket: null,
      timeout: 2000, // 45秒一次心跳
      timeoutObj: null, // 心跳心跳倒计时
      serverTimeoutObj: null, // 心跳倒计时
      timeoutnum: null, // 断开 重连倒计时
      lockReconnect: false, // 防止
      websocket: null,
      btnsList: [
        {
          label: "签出",
          label2: "签入",
          type: null,
          value: 4,
          src: require("@/assets/docCenter/checkout.png"),
          src2: require("@/assets/docCenter/checkin.png"),
          icon: "el-icon-d-arrow-right",
          icon2: "el-icon-d-arrow-left",
          permi: ["techDocManage:result:moveOutFile"],
          id: 123,
          // disabled: true,
        },
      ],
      // 签出id
      checkdata: {},
      // 服务ip
      initCurrentIP: "",
      // 人员类别
      category: "",
      // 时间类别
      timeCategory: "",
      seletctData: [],
      showAuth: false,
      authForm: {
        range: "department",
        content: "",
      },
      // 过滤文本
      filterText: "",
      // 部门岗位树
      deptOptionsTree: [],
      // 岗位树
      postOptionsTree: [],
      // 默认展开节点
      defaultExpanded: [],
      // 默认选中节点
      defaultChecked: [],
      defaultCheckedPost: [],
      // 权限机构id
      deptId: [],
      // 权限机构名称
      deptName: [],
	  tableView:true,
	  //文件夹分享弹框
	  shareDocVisible:false,
	  shareDocAddress:null
    };
  },
  watch: {
	  selectLeftType: {
	    handler(val) {
			this.tableView=false
			this.$nextTick(()=>{
				this.tableView=true
				this.$refs.fileTable.doLayout()
			})
		},
	    deep: true,
	  },
    btns: {
      handler(val) {},
      immediate: true,
      deep: true,
    },
    collectEvent: {
      handler(val) {
        switch (val) {
          case false:
            this.category = "上传人";
            this.timeCategory = "上传时间";
            break;
          case 4:
            this.category = "删除人";
            this.timeCategory = "删除时间";
            break;
        }
      },
    },
    seletctData: {
      handler(val) {
        if (val.length == 0) {
          this.visible = false;
        }
      },
    },
    treeData: {
      handler(val) {
        this.ergodicTree(val);
      },
    },
    // authForm: {
    //   handler(val) {
    //     if (val.range === "department") {
    //       this.deptOptionsTree = this.deptTree;
    //     } else if (val.range === "post") {
    //       this.deptOptionsTree = this.optionsList;
    //     }
    //   },
    //   immediate: true,
    //   deep: true,
    // },
    leftCheckedNodeId: {
      handler(val) {
        // 部门树结构
        let id = {
          id: val,
        };
        this.deptTree = [];
        this.deptOptionsTree = [];
        this.defaultChecked = [];
        userService.deptTreeListNew(id).then((res) => {
          let data = res.data.map((org) => this.mapTreeDept(org));
          this.deptTree = data;
          this.deptOptionsTree = data;
        });
        userService.optionSelectNew(id).then((res) => {
          this.postOptionsTree = res.data.map((org) =>
            this.mapTreePost(org)
          );
        });
      },
      deep: true,
    },
  },
  created() {
    this.init();
    let flag = checkPermi(["techdocmanage:collect:add"]);
  },
  computed: {
    // 根据当前窗口高度动态设置el-table的显示高度，避免出现不必要的滚动条
    getTableHeight() {
      return this.screenHeight - 270;
    },
  },
  mounted() {
    // 窗口或页面被调整大小时触发事件
    window.onresize = () => {
      // 获取body的高度
      this.screenHeight = document.body.clientHeight;
    };
    // 本机ip
    //
    //
    getConfigKey("fileAssistantDownload").then((response) => {
      this.initCurrentIP = response.msg;
    });
    getUserProfile().then((response) => {
      this.userInfo = response.data;
    });
    this.seletctData = this.$refs.fileTable.selection;
  },
  methods: {
    checkPermi,

    // 批量下载方法（参考tableHandle.vue中的download方法）
    packageDownload(list) {
      let params = {
        userId: this.$store.state.user.userId,
        resultParent: this.leftCheckedNode.id,
        businessDirectoryId: this.leftCheckedNode.businessDirectoryId,
        ids: list.map(item => {
          return item.docId.toString()
        }).join(',')
      };

      userService
        .packageDownloadDirectory(params)
        .then((res) => {
			this.$message.success("文件下载中,请在[下载记录]中查看");
          if (res.code === 200) {
            this.$emit('getList', this.leftCheckedNode);
            this.visible = false;
          }
        })
        .catch((error) => {
          this.$message.error('打包下载失败');
          console.error('打包下载错误:', error);
        });
    },


    shouldShowCheckbox(data) {
      if(this.authForm.range === 'post' && data.type === '1'){
        return true;
      }
    },

    handleCheckChange(data, checked) {
      if (checked) {
        this.$refs.treePost.setChecked(data.id, true)
      } else {
        this.$refs.treePost.setChecked(data.id, false)
      }
    },

    init() {
      if (typeof WebSocket === "undefined") {
        alert("您的浏览器不支持socket");
      } else {
        this.socket = new WebSocket(this.websocketUrl.socketRoot); // 实例化socket
        this.socket.onopen = this.open; // 监听socket连接
        this.socket.onerror = this.error; // 监听socket错误信息
        this.socket.onmessage = this.getMessage; // 监听socket消息
        this.socket.onclose = this.close; // 关闭socket消息
      }
      // }
    },
    open(row) {
      if (this.checkOutIn) {
        if (this.socket.readyState === 1) {
          let id = this.checkout(this.checkdata);
        }
      }
      console.log("wbsocket连接成功");
      clearInterval(this.errorSetInerval);
    },
    error() {
      console.log("连接错误");
    },
    async getMessage(msg) {
      let data = msg.data.split(":");

      let status = "";
      if (data[0] == "checkout status" && data[1] == "1") {
        this.$message.success("签出成功");
        this.$emit("getList", this.leftCheckedNode);
      } else if (data[0] == "checkout status" && data[1] == "0") {
        this.$message.error("签出失败");
      } else if (data[0] == "checkin status" && data[1] == "1") {
        this.$message.success("签入成功");
        this.$emit("getList", this.leftCheckedNode);
      } else if (data[0] == "checkin status" && data[1] == "0") {
        this.$message.error("签入失败");
      }

      return status;
    },
    close() {
      console.log("socket已经关闭");
      //
    },
    /** 更多操作事件 */
    handleCommand(e) {
      let list = this.$refs.fileTable.selection;
      this.$refs.tableHandle.handleCommand(e, list);
    },
    /** 文件表格按钮事件分配 */
    filetableHandle(e, row) {
      console.log(e, row);
      if (this.btns.length === 2) {
        switch (e) {
          case 0:
            this.restore(row);
            break;
          case 1:
            this.deepDelete(row);
            break;
          default:
            break;
        }
      } else {
        switch (e) {
          case 0:
            this.favorite(row);
            break;
          case 1:
            if (row) {
              this.downloadData = row;
              this.downloadType(1);
            }else{
				this.visible = true;
				this.downloadData = undefined;
			}
            break;
          case 2:
            this.share(row);
            break;
          case 3:
            this.moveTo(row);
            break;
          case 4:
            if (
              (row.createBy == this.$store.state.user.nickName &&
                (row.fileStatus == "2" || row.fileStatus == "3")) ||
              (this.userInfo.admin &&
                (row.fileStatus == "2" || row.fileStatus == "3"))
            ) {
              this.checkout(row);
            } else {
              this.$message.info(
                "只有上传人和管理员，归档状态和发布状态可签出"
              );
            }
            break;
          case 5:
            this.deleteFile(row);
            break;
          case 6:
            this.viewDetails(row);
            // this.$refs.detailView.open(row);
            break;
          case 7:
            this.modify(row);
            break;
          case 8:
            this.labelSet(row);
            break;
          case 89:
            // 查看权限
            this.showAuthFuc([row]);
            break;
          case 99:
            // 文件对话
            // if (row.docType === 'file') {
            this.$emit("fileChat", [row]);
            // }else{
            //   this.$message.error('文件夹无法操作')
            // }
            break;
          case 101:
            // if (row.docType === 'file') {
            // 智能核稿
            this.$emit("aiReviewDraft", row);
            // }else{
            //   this.$message.error('文件夹无法操作')
            // }

            break;
          default:
            break;
        }
      }
    },
    // 权限设定
    showAuthFuc(list) {
      console.log("查看权限1", list);
      let fileState = list.some((val, index, arr) => {
        return arr[index].fileStatus == "4" ? true : false;
      });
      if (fileState) {
        this.$message.info("选中的文件已无效，无法进行操作!");
        return;
      } else {
        let isChecked = list.every((val, index, arr) => {
          return arr[index].docType == "directory" ? false : true;
        });
        if (isChecked) {
          if (list.length == 1) {
            let ids = [];
            list.forEach((element) => {
              ids.push(element.id);
            });
            let id = ids.toString();
            userService.singleFileTree(id).then((res) => {
              if (res.code === 200) {
                let dataId = []
                this.authForm.range = res.data[id][0].authRange
                console.log("权限:", res.data);
                res.data[id].forEach((element) => {
                  console.log("部门权限：", element.authId)
                  dataId.push(element.authId);
                })
                if( this.authForm.range === 'department'){
                  this.$refs.tree.setCheckedKeys(dataId)
                  // this.$refs.treePost.setCheckedKeys(this.$store.state.user.postIds)
                }else {
                  console.log("岗位权限：", dataId)
                  this.$refs.treePost.setCheckedKeys(dataId)
                  // this.$refs.tree.setCheckedKeys([this.$store.state.user.deptId])
                }
              }
            });
            this.authForm.ids = ids;
            this.authForm.content = list[0].description;
            this.showAuth = true;
          } else {
            this.$message.info("暂不支持多文件修改权限，请单文件进行修改");
          }
        } else {
          this.$message.info("不能对文件夹进行操作");
        }
      }
    },
    // 标签设置
    labelSet(row) {
      if (row) {
        let data = {
          docList: [row],
          // labelType: 1 个人文档   2 知识体系  3培训专区
          labelType: 2,
        };
        this.$refs.labelOpen.open(data);
      } else {
        let docList = this.$refs.fileTable.selection;
        let data = {
          docList: docList,
          // labelType: 1 个人文档   2 知识体系  3培训专区
          labelType: 2,
        };
        this.$refs.labelOpen.open(data);
      }
    },
    /** 移动弹框树点击事件 */
    handleNodeClick(data) {
      this.isMove = data.disabled || data.type == "directory";
      if (data.disabled || data.type == "directory") {
        this.fileMove = data;
      } else {
        return this.$message.warning("不可在此层目录进行文件移动");
      }
    },
    /** 彻底删除 */
    deepDelete(row) {
      let deleteId;
      if (row) {
        deleteId = row.id;
      } else {
        let list = this.$refs.fileTable.selection;
        let ids = [];
        list.forEach((element) => {
          ids.push(element.id);
        });

        deleteId = ids.toString();
      }

      this.$confirm("彻底删除后将无法找回，是否彻底删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        this.$emit("clearFile", deleteId);
      });
    },
    /** 还原 */
    restore(row) {
      let restoreId;
      if (row) {
        restoreId = row.docId;
      } else {
        let list = this.$refs.fileTable.selection;
        let ids = [];
        list.forEach((element) => {
          ids.push(element.docId);
        });

        restoreId = ids.toString();
      }

      this.$confirm("还原后将恢复至原文件夹下，是否还原？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$emit("recoverFile", restoreId);
      });
    },

    /** 收藏 */
    favorite(row) {
      if (row) {
        if (row.fileStatus == "4") {
          this.$message.info("文件已无效，无法进行操作!");
          return;
        }

        // this.$emit("collectFile", [row]);
        // flag-0 表示未收藏
        if (row.flag == "0") {
          if (row.docType === "file") {
            let id = row.id;
            userService.collectFile(id).then((res) => {
              if (res.code === 200) {
                this.$message.success("收藏成功");
                this.$emit("getList", this.leftCheckedNode);
              }
            });
          } else {
            this.$message.error("该文件夹不能收藏");
          }
        } else {
          if (row.docType === "file") {
            let id = row.id;
            userService.personalCollectionDelete(id).then((res) => {
              if (res.code === 200) {
                this.$message.warning("取消收藏");
                this.$emit("getList", this.leftCheckedNode);
              }
            });
          } else {
            this.$message.error("文件夹无法操作");
          }
        }
      } else {
        let list = this.$refs.fileTable.selection;
        let fileState = list.some((val, index, arr) => {
          return arr[index].fileStatus == "4" ? true : false;
        });

        if (fileState) {
          this.$message.info("选中的文件已无效，无法进行操作!");
          return;
        } else {
          let isChecked = list.every((val, index, arr) => {
            return arr[index].docType == "directory" ? false : true;
          });
          if (isChecked) {
            let ids = [];
            list.forEach((item) => {
              ids.push(item.id);
            });
            userService.collectFile(ids).then((res) => {
              if (res.code === 200) {
                this.$message.success("收藏成功");
                this.$emit("getList", this.leftCheckedNode);
              }
            });
          } else {
            this.$message.error("文件夹不能收藏");
          }
        }
      }
    },
	  downloadFile(url, fileName) {
	  let xhr = new XMLHttpRequest();
	  xhr.open('GET', url, true);
	  xhr.responseType = 'blob';
	  xhr.onload = function() {
	    let blob = new Blob([xhr.response]);
		const a = document.createElement("a");
	    let url = URL.createObjectURL(blob);
	     a.href = url;
         a.download = fileName;
         a.click();
         window.URL.revokeObjectURL(url);
	  }
	  xhr.send();
	},
    downloadType(type) {
      let row = this.downloadData;
	  //下载文档下载
	  if(this.selectLeftType=='0'){
		  window.open(row.taskMetadata, "_blank");
		  //this.downloadFile(row.taskMetadata,row.taskName)
		  return
	  }
      switch (type) {
        case 0:
          if (row) {
            if (row.fileStatus == "4") {
              this.$message.info("文件已无效，无法进行操作!");
              return;
            }
            // 如果是文件夹，使用批量下载
            if (row.docType !== "file") {
              this.packageDownload([row]);
              return;
            }
            // this.$emit('downloadFile', row)

            let list = [row];
            let data = {
              id: list[0].docId.toString(),
            };
            userService.downloadSourceFile(data).then((res) => {
              let blob = new Blob([res], {
                type: "application/pdf;charset=utf-8",
              });
              blob.text().then((result) => {
                try {
                  let res = JSON.parse(result);
                  if (res.code === 500) {
                    this.$message.error(res.msg);
                    return;
                  }
                } catch (error) {
                  let objectUrl = URL.createObjectURL(blob);
                  let link = document.createElement("a");
                  // 源文件下载
                  link.download = list[0].resultName;
                  link.href = objectUrl;
                  link.click();
                  link.remove();
                  this.visible = false;
                }
              });
            });
          } else {
            let list = this.$refs.fileTable.selection;

            let fileState = list.some((val, index, arr) => {
              return arr[index].fileStatus == "4" ? true : false;
            });
            let fileType = list.some((val, index, arr) => {
              return arr[index].docType !== "file" ? true : false;
            });
            if (fileState) {
              this.$message.info("选中的文件已无效，无法进行操作!");
              return;
            } else {
              // 如果包含文件夹，使用批量下载
              if (fileType) {
                this.packageDownload(list);
              } else {
                // 纯文件列表，使用原有的单文件下载逻辑
                list.forEach((item) => {
                  let data = {
                    id: item.docId.toString(),
                    userId: this.$store.state.user.userId,
                  };
                  userService.downloadSourceFile(data).then((res) => {
                    let blob = new Blob([res]);
                    blob.text().then((result) => {
                      try {
                        let res = JSON.parse(result);
                        if (res.code === 500) {
                          this.$message.error(res.msg);
                          return;
                        }
                      } catch (error) {
                        let objectUrl = URL.createObjectURL(blob);
                        let link = document.createElement("a");
                        // 源文件下载
                        link.download = item.resultName;
                        // }
                        link.href = objectUrl;
                        link.click();
                        link.remove();
                        this.visible = false;
                      }
                    });
                  });
                });
              }
            }
          }
          break;
        case 1:
          if (row) {
            if (row.fileStatus == "4") {
              this.$message.info("文件已无效，无法进行操作!");
              return;
            }
            if (row.docType !== "file") {
              this.packageDownload([row]);
              return;
            }
            this.$emit("downloadFile", row);
            this.visible = false;
          } else {
            let list = this.$refs.fileTable.selection;
            let fileState = list.some((val, index, arr) => {
              return arr[index].fileStatus == "4" ? true : false;
            });
            let fileType = list.some((val, index, arr) => {
              return arr[index].docType !== "file" ? true : false;
            });
            if (fileState) {
              this.$message.info("选中的文件已无效，无法进行操作!");
              return;
            } else {
              if (fileType) {
                this.$message.info("选中文件中包含文件夹，无法下载");
              } else {
                list.forEach((item) => {
                  this.$emit("downloadFile", item);
                  this.visible = false;
                });
              }
            }
          }
          break;
        default:
          break;
      }
    },
    /** 下载 */
    download(row) {
      if (row) {
        if (row.fileStatus == "4") {
          this.$message.info("文件已无效，无法进行操作!");
          return;
        }
        if (row.docType !== "file") {
          this.$message.info("正在开发中，敬请期待！");
          return;
        }
        this.$emit("downloadFile", row);
      } else {
        let list = this.$refs.fileTable.selection;
        let fileState = list.some((val, index, arr) => {
          return arr[index].fileStatus == "4" ? true : false;
        });
        if (fileState) {
          this.$message.info("选中的文件已无效，无法进行操作!");
          return;
        } else {
          if (list.length == 1) {
            if (list[0].docType !== "file") {
              this.packageDownload([row]);
              return;
            }
            this.$emit("downloadFile", list[0]);
          } else {
            this.$message.info("不支持多文件下载或者不是文件");
          }
        }
      }
    },
    /** 分享 */
    share(row) {
      if (row) {
        if (row.fileStatus == "4") {
          this.$message.info("文件已无效，无法进行操作!");
          return;
        }
        if (row.docType == "file") {
          this.fileId = row.id;
          this.$refs.shareView.showShare = true;
        } else {
		  addShareDoc({id:row.id}).then(res=>{
        this.shareDocAddress=window.location.origin+process.env.VUE_APP_BASE_API+res.msg;
        this.shareDocVisible=true
		  })
		  // this.$emit("shareFile",)
        }
      } else {
        let list = this.$refs.fileTable.selection;
        let fileState = list.some((val, index, arr) => {
          return arr[index].fileStatus == "4" ? true : false;
        });
        if (fileState) {
          this.$message.info("选中的文件已无效，无法进行操作!");
          return;
        } else {
          let isChecked = list.every((val, index, arr) => {
            return arr[index].docType == "directory" ? false : true;
          });
          if (isChecked) {
            let ids = [];
            list.forEach((element) => {
              ids.push(element.id);
            });

            this.fileId = ids.toString();
            this.$refs.shareView.showShare = true;
          } else {
            this.$message.warning("文件夹暂不能分享");
          }
        }
      }
    },
    /** 监听分享回调 */
    shareSet(e) {
      let data = {
        type: "businessSharing",
        fileIds: this.fileId,
        receiveIds: e.receiveIds.toString(),
        receiveBys: e.receiveBys.toString(),
        operation: e.operation,
        endTime: e.endTime,
        shareTime: e.shareTime,
      };

      userService.shareFile(data).then((res) => {
        if (res.code === 200) {
          this.$message.success("分享成功");
          // this.$refs.shareView.showShare = false;
        }
      });
    },
    /** 移动 */
    submitMove(e) {
      if (this.fileMove.type === "business") {
        let data = {
          ids: this.fileMoveId,
          parent: -1,
          businessDirectoryId: this.fileMove.id,
        };

        this.$emit("moveFile", data);
      } else {
        let data = {
          ids: this.fileMoveId,
          parent: this.fileMove.id,
          businessDirectoryId: this.fileMove.businessDirectoryId,
        };

        this.$emit("moveFile", data);
      }
      this.showMove = false;
    },
    moveTo(row) {
      this.$confirm(
        "移动后文件夹下的所有文件或子文件夹权限将发生改变？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        if (row) {
          if (row.fileStatus == "4") {
            this.$message.info("文件已无效，无法进行操作!");
            return;
          }
          this.fileMoveId = row.id;
          this.showMove = true;
        } else {
          let list = this.$refs.fileTable.selection;
          let fileState = list.some((val, index, arr) => {
            return arr[index].fileStatus == "4" ? true : false;
          });
          if (fileState) {
            this.$message.info("选中的文件已无效，无法进行操作!");
            return;
          } else {
            let ids = [];
            list.forEach((element) => {
              ids.push(element.id);
            });

            this.fileMoveId = ids.toString();
          }
          this.showMove = true;
        }
      });
    },
    // 获取本地token值
    getCookie(name) {
      let strCookie = document.cookie;
      let arrCookie = strCookie.split(";");
      for (let i = 0; i < arrCookie.length; i++) {
        let arr = arrCookie[i].split("=");
        if (arr[0].replace(/(^\s*)|(\s*$)/g, "") == name) {
          return arr[1];
        }
      }
      return "";
    },
    /** 签出 */
    checkout(row) {
      this.checkdata = row;
      var checkId = "";
      // 0签出-上锁 表示可以进行签入      1签入-解锁 表示可以签出
      if (row) {
        if (row.docType !== "directory") {
          if (row.locked == 1 || row.locked == null) {
            if (this.socket.readyState === 1) {
              console.log("wbsocket连接成功");
              // 签出标识
              const checkState = "checkout";
              // 主键id
              // let id = row.id
              // 获取token值
              let tokenId = this.getCookie("Admin-Token");
              // 获取签出文件名
              let fileName = row.resultName;
              // 获取用户Id
              let userId = this.$store.state.user.userId;
              let data = [row.id, userId, tokenId, fileName];
              let id = checkState + ":" + data.toString();
              // this.checkOutId = id;
              // checkId = id

              // 发送webSocket请求
              this.socket.send(JSON.stringify(id));
            } else if (this.socket.readyState === 3) {
              console.log("socket已经关闭");
              this.checkOutIn = true;
            }
          } else if (row.locked == 0) {
            // 0签出-上锁 表示可以进行签入
            if (this.socket.readyState === 1) {
              console.log("wbsocket连接成功");
              const h = this.$createElement;
              const objstatusTr = h(
                "input",
                {
                  style: { margin: "10px 10px" },
                  attrs: {
                    name: "statusR",
                    type: "radio",
                    value: 0,
                    checked: this.statusclickTr,
                  },
                  on: {
                    click: function (event) {
                      this.flag = event.target.value;
                    }.bind(this),
                  },
                },
                ""
              );
              const objstatusFa = h(
                "input",
                {
                  style: { margin: "10px 10px" },
                  attrs: {
                    name: "statusR",
                    type: "radio",
                    value: 1,
                    checked: !this.statusclickTr,
                  },
                  on: {
                    click: function (event) {
                      this.flag = event.target.value;
                    }.bind(this),
                  },
                },
                ""
              );
              this.$msgbox({
                title: "版本选择",
                closeOnClickModal: false,
                message: h("p", null, [
                  objstatusTr,
                  "版本",
                  objstatusFa,
                  "版次",
                ]),
              })
                .then((action) => {
                  if (action === "confirm") {
                    // 签出标识
                    const checkState = "checkin";
                    // 获取token值
                    let tokenId = this.getCookie("Admin-Token");
                    // 获取签出文件名
                    let fileName = row.resultName;
                    // 获取用户Id
                    let userId = this.$store.state.user.userId;
                    // 获取版本(0)或版次(1)
                    let flag = this.flag;
                    let data = [row.id, tokenId, this.flag, fileName, userId];
                    let id = checkState + ":" + data.toString();
                    // checkId = checkState + ":" + data.toString()
                    // this.checkInId = id;
                    // 发送webSocket请求

                    this.socket.send(JSON.stringify(id));
                    return id;
                  }
                })
                .catch((e) => {});
            } else if (this.socket.readyState === 3) {
              console.log("socket已经关闭");
              this.checkOutIn = true;
            }
          }
        } else {
          this.$message.warning("不能对文件夹进行操作");
        }
      } else {
        this.$message.warning("暂不支持多文件签出");
      }

      // return checkId
    },
    // 打开签入签出弹框
    openCheckOut() {
      this.checkOutIn = true;
    },
    // 触发websocket
    oneTouchStart() {
      location.href = "FileAssistant://";
      this.init();
      this.errorSetInerval = setInterval(() => {
        this.init();
      }, 2000);
    },
    // 下载文件助手
    dowloadClick() {
      location.href = this.initCurrentIP + "/FileAssistant/FileAssistant.exe";
    },
    /** 删除 */
    deleteFile(row) {
      if (row) {
        this.$emit("deleteFile", false, row.id);
      } else {
        let list = this.$refs.fileTable.selection;

        this.$confirm("是否删除选中的文件或文件夹？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let ids = [];
            list.forEach((element) => {
              ids.push(element.id);
            });

            let id = ids.toString();
            userService.deleteDirectory(id).then((res) => {
              if (res.code === 200) {
                this.$message.success("删除成功");
                this.$emit("getList", this.leftCheckedNode);
                this.$emit("getTree",);
              }
            });
          })
          .catch();
      }
    },
    /** 查看详细信息 */
    viewDetails(row) {
      if (row.fileStatus == "4") {
        this.$message.info("文件已无效，无法进行操作!");
        return;
      }
      this.$refs.detailView.open(row, true);
    },
    // 修改信息
    modify(row) {
      if (row) {
        if (row.fileStatus == "4") {
          this.$message.info("文件已无效，无法进行操作!");
          return;
        }
        let e = Number(row.fileStatus);
        if (e === 0) {
          this.$refs.modifyFile.open(row);
        } else {
          this.$message.info("仅可以在创建状态下进行文件修改");
        }
      } else {
        let list = this.$refs.fileTable.selection;
        let fileState = list.some((val, index, arr) => {
          return arr[index].fileStatus == "4" ? true : false;
        });
        if (fileState) {
          this.$message.info("选中的文件已无效，无法进行操作!");
          return;
        } else {
          if (list.length == 1) {
            let e = Number(list[0].fileStatus);
            if (e === 0) {
              this.$refs.modifyFile.open(list[0]);
            } else {
              this.$message.info("仅可以在创建状态下进行文件修改");
            }
          } else {
            this.$message.info("不支持多文件修改");
          }
        }
      }
    },
    /** 文件名列点击事件 */
    clickFilename(row) {
		//如果是文档下载 点击直接下载
		if(this.selectLeftType=='0'){
			this.downloadData=row;
			this.downloadType()
		}else if (row.docType === "file") {
        this.$store.commit("common/PDFDOCID", row.docId);
        this.$emit("previewFile", row);
      } else {
        this.$emit("tableDataList", row);
        this.$emit("setTreeNode", row);
      }
    },
    /** 表格多选框切换事件 */
    handleSelectionChange(e) {
      this.$emit("selectionChange", e);
    },
    /** 鼠标进入列事件 */
    showOperation(row) {
      this.operationShowFileId = row.id;
    },
    /** 鼠标移出列事件 */
    hiddenOperation() {
      this.operationShowFileId = "";
    },
    // 状态更新
    getState(data) {
      let e = Number(data);
      switch (e) {
        case 0:
          return "创建";
        case 1:
          return "提交";
        case 2:
          return "归档";
        case 3:
          return "发布";
        case 4:
          return "作废";
      }
    },
    getVectorState(convertVectorStatus){
      let type = {
        '0':'入库中',
        '1':'已入库',
      }
      return type[convertVectorStatus]
    },
    // 列表颜色渲染
    tableRowClassName({ row }) {
      if (row.invalid) {
        return "warning-row";
      }
      return "";
    },
    ergodicTree(data) {
      this.newTreeData = data.map((org) => this.mapTree(org));
    },
    // 禁用树结构选中
    mapTree(org) {
      const haveChildren =
        Array.isArray(org.children) && org.children.length > 0;
      return {
        //分别将我们查询出来的值做出改变他的key
        id: org.id,
        label: org.treeName,
        businessDirectoryId: org.businessDirectoryId || org.id,
        disabled: org.childIsFolder,
        type: org.type || org.docType,
        //判断它是否存在子集，若果存在就进行再次进行遍历操作，直到不存在子集便对其他的元素进行操作
        children: haveChildren
          ? org.children.map((i) => this.mapTree(i))
          : null,
      };
    },
    // 刷新列表
    getListChild() {
      this.$emit("getList");
    },
    closeAuth() {
      // this.authForm.range = "";
      // this.deptOptionsTree = [];
      this.showAuth = false;
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 新建文件夹- 获取点击权限组织树结构的数据
    getOrganizationTree(data) {
      this.deptId = [];
      this.deptName = [];
      this.$refs.tree.getCheckedNodes().forEach((element) => {
        this.deptId.push(element.id);
        this.deptName.push(element.label);
      });
    },
    // 递归遍历树形结构
    mapTreeDept(org) {
      let deptId = this.$store.state.user.deptId;
      const haveChildren =
        Array.isArray(org.children) && org.children.length > 0;
      return {
        //分别将我们查询出来的值做出改变他的key
        label: org.label,
        disabled: Boolean(deptId == org.id),
        id: org.id,
        //判断它是否存在子集，若果存在就进行再次进行遍历操作，直到不存在子集便对其他的元素进行操作
        children: haveChildren
          ? org.children.map((i) => this.mapTreeDept(i))
          : null,
      };
    },
    mapTreePost(org) {
      console.log("岗位处理：" ,org);
      // 如果 type 为 0 且 children 为空，则返回 null 表示过滤掉该节点
      if (org.type === '0' && (org.children === null || org.children.length === 0) ) {
        return null;
      }
      let postIds = this.$store.state.user.postIds;
      const haveChildren = Array.isArray(org.children) && org.children.length > 0;

      const node = {
        label: org.isLeaf === 0 ? org.label + '(部门)' : (org.isLeaf === 1 ? org.label + '(岗位)' : org.label + '(人员)'),
        disabled: Boolean(postIds.includes(org.id)),
        isLeaf: org.isLeaf,
        type: org.type,
        id: org.id,
        children: haveChildren ? org.children.map((i) => this.mapTreePost(i)).filter(Boolean) : null,
      };
      return node;
    },
    getStateTagType(fileStatus) {
      let type = '';
      switch (Number(fileStatus)) {
        case 3: // 发布
          type = 'primary';
          break;
        case 4: // 作废
          type = 'warning';
          break;
        default: // 创建、提交、归档等其他状态
          type = 'info';
          break;
      }
      return type;
    },
    getVectorTagType(convertVectorStatus) {
      let type = '';
      switch (Number(convertVectorStatus)) {
        case 0: // 入库中
          type = 'warning';
          break;
        case 1: // 已入库
          type = 'success';
          break;
        default:
          type = 'info';
          break;
      }
      return type;
    },
    getDirButtons(){
      // 下载
      const defaultValues = [1,2,3]; // 下载
      // return this.btns.filter(btn => defaultValues.includes(btn.value));

      return this.btns.filter(btn => {
        // 原来判断
        if (!defaultValues.includes(btn.value)) {
          return false;
        }

        // 文件夹分享按钮增加额外权限判断
        if (btn.value === 2) {
          return checkPermi(['techDocManage:folderShare']) && checkPermi(btn.permi);
        }
        return checkPermi(btn.permi);
      });
    },
    // 获取默认按钮组（下载、分享、删除、智能修订）
     getDefaultButtons(row) {
        // 如果是文档下载 只有一个下载 和删除
        if (this.selectLeftType=='0' ) {
        let btnArr=row.taskStatus=='下载完毕'?[1,5]:[5]
        return  this.btns.filter(btn => btnArr.includes(btn.value)).reverse();
        }
       // 如果是回收站，直接显示所有按钮
       if (this.btns.length === 2) {
         return this.btns;
       }

       // 普通文件模式，只显示下载、分享、删除、智能修订
       const defaultValues = [6, 1, 2, 0]; // 详细信息，下载、分享
       return this.btns.filter(btn => defaultValues.includes(btn.value));
     },
     // 保留原有的getMoreButtons方法
    getMoreButtons() {
      // 如果是回收站或者文档下载，没有更多按钮
      if (this.btns.length === 2||this.selectLeftType=='0') {
        return [];
      }
      console.info(this.btns)
      // 普通文件模式，其他按钮放到更多菜单中
      const defaultValues = [6, 1, 2, 0, 5 ,8, 99]; // 下载、分享、删除、智能修订 不需要删除、文件对话、标签设置
      return this.btns.filter(btn => !defaultValues.includes(btn.value));
    },
     // 获取更多操作按钮组--过滤
     authMoreButtonsFilter(row, moreButtons) {
         // 获取文件扩展名
      const fileName = row.userDocName || row.resultName || row.docName || '';
      const fileExtension = fileName.toLowerCase().split('.').pop();
        // 如果是PDF文件，过滤掉智能修订按钮(101)
      if (fileExtension === 'pdf') {
          moreButtons = moreButtons.filter(btn => btn.value !== 101);
      }
       return moreButtons;
     },
     // 处理下拉菜单命令
     handleDropdownCommand(command, row) {
       this.filetableHandle(command, row);
     },
  },
  destroyed() {
    this.socket.close(); // 关闭连接
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  height: calc(100% - 50px);
  .handle-icon-line {
    display: flex;
    align-items: center;
    .handle-icon {
      margin-right: 10px;
      width: 18px;
      cursor: pointer;
    }
    .permiHidden {
      display: none;
    }
    .permiShow {
      display: inline-block;
    }
         .default-buttons {
       display: inline-flex;
       align-items: center;
       gap: 8px;
     }

     .more-dropdown {
       display: inline-flex;
       align-items: center;
       margin-left: 8px;
     }

     .more-btn {
       cursor: pointer;
       font-size: 18px;
       color: #606266;
       padding: 5px;
       border-radius: 4px;
       display: inline-flex;
       align-items: center;
       justify-content: center;
       transition: all 0.3s;
       &:hover {
         color: #409eff;
         background-color: #f5f7fa;
       }
     }
  }
}
.el-table th > .cell {
  padding-left: 14px;
}

::v-deep .el-table td {
  vertical-align: middle;
  padding:0;
}

::v-deep .el-table td .cell {
  padding-left: 14px;
  padding-right: 14px;
}
.file-operation-content {
  display: inline-block;
  position: absolute;
  left: 320px;
  top: 12px;
  z-index: 2;
  background: #f5f7fa;
  width: 40%;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.file-content-card {
  display: flex;
  align-items: center;
  img {
    width: 40px;
    height: 40px;
    margin-right: 16px;
  }
  .card-style {
    flex: 1;
    font-size: 14px;
    &__filename {
      font-size: 14px;
      font-weight: bold;
      line-height: 2.2;
    }
  }

  .card-info-text {
    line-height: 1.4;
    color: #606266;
    font-size: 12px;
  }
}

// 文件名称样式
.file-content {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
}

.file-name-wrapper {
  flex: 1;
  min-width: 0;
  cursor: pointer;
}

.file-name-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  max-height: 2.8em; /* 2行的高度 */
  color: #303133;
  font-size: 14px;

  &:hover {
    color: #409eff;
  }
}

.table-cell-content {
  display: flex;
  align-items: center;
  height: 100%;
  min-height: 44px;
}
::v-deep .el-dialog__body {
  padding: 15px 20px 0;
}
.tree-wrap {
  height: 360px;
  overflow-y: auto;
}
.share-box {
  display: flex;
  .share-left {
    flex: 1;
    padding: 0 10px;
    .person-tree {
      height: 240px;
      overflow-y: auto;
    }
  }
  .share-right {
    flex: 1;
    > div:nth-child(1) {
      line-height: 32px;
    }
    .checked-list-wrap {
      height: 240px;
      overflow-y: auto;
      line-height: 2;
      .checked-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .cls-btn {
          padding-right: 5px;
          font-size: 16px;
          line-height: 1;
          cursor: pointer;
        }
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}
.checkOutIn {
  display: flex;
  justify-content: center;
  .check {
    width: 225px;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    // background-color: pink;
    div {
      margin: 10px;
    }
  }
}
.el-divider--vertical {
  display: inline-block;
  width: 1px;
  height: 200px;
  margin: 0 8px;
  vertical-align: middle;
  position: relative;
}
::v-deep .el-table .warning-row {
  background: #e8eaec6b !important;
}
.keyButton.el-button {
  width: 300px;
  overflow: hidden;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 0 !important;
  background: transparent;
  padding: 0 !important;
  white-space: pre-wrap;
  line-height: initial;
}
.keyButton.el-button:hover {
  border: 0 !important;
  background: transparent !important;
  padding: 0 !important;
}
.dowload {
  position: relative;
  top: -90%;
  right: -45%;
}

.file-status-tag {
  text-align: center;
  width: 50px;
  margin: 0 10px;
}
</style>
