<template>
  <div>
    <!-- 密级设定 -->
    <el-dialog title="密级设定" :visible.sync="showGrade" width="520px" @close="closeGrade">
      <el-form label-width="84px" :form="gradeForm">
        <el-form-item label="密级">
          <el-radio-group v-model="gradeForm.grade">
            <el-radio :label="'A'">A级</el-radio>
            <el-radio :label="'B'">B级</el-radio>
            <el-radio :label="'C'">C级</el-radio>
            <el-radio :label="'D'">D级</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注说明">
          <el-input type="textarea" :rows="4" v-model="gradeForm.content" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeGrade">取 消</el-button>
        <el-button type="primary" @click="submitGrade">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 权限设定 -->
    <el-dialog title="权限设定" :visible.sync="showAuth" width="520px" @close="closeAuth">
      <el-form label-width="84px" :form="authForm">
        <el-form-item label="权限组织" prop="range">
          <el-radio-group v-model="authForm.range">
            <el-radio :label="'department'">部门</el-radio>
            <el-radio :label="'post'">岗位</el-radio>

          </el-radio-group>
		  <el-input placeholder="输入关键字进行过滤" v-model="filterText">
		              </el-input>
		  <!--            <el-tree node-key="id" class="filter-tree" :data="deptOptionsTree"  :show-checkbox="true" :default-expand-all="true" :default-expanded-keys="defaultExpanded" :default-checked-keys="defaultChecked" :filter-node-method="filterNode" @check="getOrganizationTree" ref="tree">-->
		  <!--            </el-tree>-->
		              <el-tree class="filter-tree"
		                       v-show="authForm.range == 'department'"
		                       :data="deptOptionsTree"
		                       :show-checkbox="true"
		                       node-key="id"
		                       :check-strictly="true"
		                       :default-expand-all="true"
		                       :default-expanded-keys="defaultExpanded"
		                       :default-checked-keys="defaultChecked"
		                       :filter-node-method="filterNode"
		                       @check="getOrganizationTree" ref="tree">
		              </el-tree>
		              <el-tree class="filter-tree"
		                       v-show="authForm.range == 'post'"
		                       :data="postOptionsTree"
		                       :show-checkbox="false"
		                       node-key="id"
		                       :default-expand-all="true"
		                       :default-expanded-keys="defaultExpanded"
		                       :default-checked-keys="defaultCheckedPost"
		                       :filter-node-method="filterNode"
		                       @check="getOrganizationTree" ref="treePost">
		                <template #default="{ node, data }">
		                      <span v-if="shouldShowCheckbox(data,node)">
		                      <el-checkbox v-model="node.checked" :disabled="node.disabled" @change="(checked) => handleCheckChange(data, checked)"></el-checkbox>
		                        {{node.label}}
		                      </span>
		                      <span v-else>
		                       {{node.label}}
		                       </span>
		                </template>
		              </el-tree>
        </el-form-item>
        <el-form-item label="备注说明" prop="content">
          <el-input type="textarea" :rows="4" v-model="authForm.content" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeAuth">取 消</el-button>
        <el-button type="primary" @click="submitAuth">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 失效 -->
    <el-dialog title="文件失效" :visible.sync="showInvalid" width="520px" @close="closeInvalid">
      <el-form label-width="84px">
        <el-form-item label="失效原因">
          <el-input type="textarea" :rows="4" v-model="invalidReason" placeholder="请输入内容" />
          <span style="color: red">注：失效后文件将无法再被使用</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeInvalid">取 消</el-button>
        <el-button type="primary" @click="submitInvalid">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import userService from '@/api/techdocmanage/docCenter/user'
import {queryFilesKnoledge} from '../uploadFileChat.js'
export default {
  props: ['leftCheckedNodeId', 'leftCheckedNode'],
  data () {
    return {
      gradeForm: {
        grade: '',
        content: '',
      },
      showGrade: false,
      authForm: {
        range: 'department',
        content: '',
      },
      showAuth: false,
      showInvalid: false,
      invalidReason: '',
      // 过滤文本
      filterText: '',
      // 文档目录ID
      fileDirectoryId: '',
      // 文档目录数据
      fileDirectoryData: [],
      // 部门岗位树
      deptOptionsTree: [],
      postOptionsTree:[],
      // 岗位树
      optionsList: [],
      // 部门树
      deptTree: [],
      // 权限机构id
      deptId: [],
      // 权限机构名称
      deptName: [],
      // 默认展开节点
      defaultExpanded: [],
      // 默认选中节点
      defaultChecked: [],
      defaultCheckedPost: [],
      embed_model:''
    }
  },
  watch: {
    filterText (val) {
      if(this.authForm.range == 'department'){
        this.$refs.tree.filter(val)
      }else if(this.authForm.range == 'post'){
        this.$refs.treePost.filter(val)
      }
    },

    'authForm.range': {
      handler(orgType) {
        if (orgType === 'department') {

        } else if (orgType === 'post') {
          console.log('切换岗位树：', this.optionsList , this.defaultChecked)
          let postId = this.$store.state.user.postIds
          console.log('岗位id：', postId)
          if (postId.length == 0){
            this.authForm.range = 'department';
            this.$message.error('当前用户未分配岗位！')
            return
          }
        }
      },
      immediate: true
    },

    leftCheckedNodeId: {
      handler (val) {
        // 部门树结构
        let id = {
          id: val,
        }
        // this.deptTree = []
        this.deptOptionsTree = []
        this.defaultChecked = []
        this.defaultCheckedPost = []
        userService.deptTreeListNew(id).then((res) => {
          let data = res.data.map((org) => this.mapTree(org))
          this.deptTree = data
          this.deptOptionsTree = data
          console.log("deptOptionsTree", this.deptOptionsTree);
          if(this.deptOptionsTree.length === 0) {
            this.authForm.range = "post"
          }

        })
        userService.optionSelectNew(id).then((res) => {
          this.postOptionsTree = res.data.map((org) => this.mapTree1(org))
          console.log("postOptionsTree", this.postOptionsTree);
          if(this.postOptionsTree.length === 0) {
            this.authForm.range = "department"
          }
        })
      },
      deep: true,
    },
  },
  created(){
        this.getConfigKey("embed_model").then((response) => {
      this.embed_model = response.msg;
    });

  },
  methods: {
    handleCommand (e, list) {
      switch (e) {
        case 0:
          this.showGradeFuc(list)
          break
        case 1:
          console.log(list);
          this.showAuthFuc(list)
          break
        case 2:
          this.archive(e, list)
          break
        case 3:
          this.publish(e, list)
          break
        case 4:
          this.invalid(e, list)
          // this.showInvalid = true;
          break
		case 5:
		  this.download(e, list)
        default:
          break
      }
    },
    // 密级设定
    showGradeFuc (list) {
      let fileState = list.some((val, index, arr) => {
        return arr[index].fileStatus == '4' ? true : false
      })
      if (fileState) {
        this.$message.info('选中的文件已作废，无法进行操作!')
        return
      } else {
        let isChecked = list.every((val, index, arr) => {
          return arr[index].docType == 'directory' ? false : true
        })
        if (isChecked) {
          if (list.length == 1) {
            let e = list[0].resultSecret
            switch (e) {
              case null:
                this.gradeForm.grade = ''
                break
              case 'A':
                this.gradeForm.grade = 'A'
                break
              case 'B':
                this.gradeForm.grade = 'B'
                break
              case 'C':
                this.gradeForm.grade = 'C'
                break
              case 'D':
                this.gradeForm.grade = 'D'
                break
            }
            let ids = []
            list.forEach((element) => {
              ids.push(element.id)
            })
            this.gradeForm.ids = ids
            this.gradeForm.content = list[0].description
            this.showGrade = true
          } else {
            this.gradeForm.grade = ''
            let ids = []
            list.forEach((element) => {
              ids.push(element.id)
            })
            this.gradeForm.content = ''
            this.gradeForm.ids = ids
            this.showGrade = true
          }
        } else {
          this.$message.info('不能对文件夹进行操作')
        }
      }
    },
    closeGrade () {
      this.showGrade = false
    },

    shouldShowCheckbox(data, node) {
      if(this.authForm.range === 'post' && data.type === '1'){
        return true;
      }
    },
    handleCheckChange(data, checked) {
      console.log("data2222222", data, checked);
      if (checked) {
        // this.selectedDeptIds.push(data.id)
        this.$refs.treePost.setChecked(data.id, true)
      } else {
        this.$refs.treePost.setChecked(data.id, false)
        // this.selectedDeptIds = this.selectedDeptIds.filter(id => id !== data.id)
      }
      // 同步到 deptId，用于后续提交
      // this.deptId = [...this.selectedDeptIds]
    },

    submitGrade () {
      let data = {
        resultSecret: this.gradeForm.grade,
        ids: this.gradeForm.ids,
        description: this.gradeForm.content,
      }

      userService.updateBusinessDirectory(data).then((res) => {

        if (res.code === 200) {
          this.$message.success('密级设定成功')
          this.$emit('getList', this.leftCheckedNode)
        }
      })
      this.showGrade = false
    },
    // 权限设定
    showAuthFuc (list) {
      console.log("查看权限2" , list);
      let fileState = list.some((val, index, arr) => {
        return arr[index].fileStatus == '4' ? true : false
      })
      if (fileState) {
        this.$message.info('选中的文件已作废，无法进行操作!')
        return
      } else {
        let isChecked = list.every((val, index, arr) => {
          return arr[index].docType == 'directory' ? false : true
        })
        if (isChecked) {
          if (list.length == 1) {
            let ids = []
            list.forEach((element) => {
              ids.push(element.id)
            })
            let id = ids.toString()
            userService.singleFileTree(id).then((res) => {
              console.log("res", res);
              if (res.code === 200) {
                let dataId = []
                let resData = res.data[id];
                let range = '';
                if(resData.length > 0){
                  range = resData[0].authRange;
                }
                resData.forEach((element) => {
                  dataId.push(element.authId)
                  // this.$refs.tree.setCheckedKeys(dataId)
                })
                if( range === 'department'){
                  this.$refs.tree.setCheckedKeys(dataId)
                  this.$refs.treePost.setCheckedKeys(this.$store.state.user.postIds)
                }else {
                  this.$refs.treePost.setCheckedKeys(dataId)
                  this.$refs.tree.setCheckedKeys([this.$store.state.user.deptId])
                }
              }
            })
            this.authForm.ids = ids
            this.authForm.content = list[0].description
            this.showAuth = true
          } else {
            this.$message.info(
              '暂不支持多文件修改权限，请单文件进行修改'
            )
          }
        } else {
          this.$message.info('不能对文件夹进行操作')
        }
      }
    },
    closeAuth () {
      // this.authForm.range = "";
      // this.deptOptionsTree = [];
      this.showAuth = false
    },
    submitAuth () {
      let checkedNodes = [];
      let deptId = [];
      let deptName = [];
      if(this.authForm.range === 'department'){
        checkedNodes = this.$refs.tree.getCheckedNodes();
        deptId = checkedNodes.map(item => item.id);
        deptName = checkedNodes.map(item => item.label);
      }else {
        checkedNodes = this.$refs.treePost.getCheckedNodes();
        deptId = checkedNodes
          .filter(item => item.type !== '0')
          .map(item => item.id);
        deptName = checkedNodes
          .filter(item =>item.type !== '0')
          .map(item => item.label);
      }

      let data = {
        ids: this.authForm.ids,
        range: this.authForm.range,
        deptId: deptId.toString(),
        deptName: deptName.toString(),
      }

      userService.updateBusinessDirectory(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('权限设定成功')
          this.$emit('getList', this.leftCheckedNode)
        }
      })
      this.showAuth = false
    },
    // 归档
    archive (e, list) {
      let isChecked = list.every((val, index, arr) => {
        return arr[index].docType == 'directory' ? false : true
      })
      if (!isChecked) {
        this.$message.info('文件夹无法操作')
        return
      }
      this.$confirm(
        '归档之后将无法删除文件，是否确认归档选中文件？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        let fileState = list.some((val, index, arr) => {
          return arr[index].fileStatus == '4' ? false : false
        })
        if (fileState) {
          this.$message.info('选中的文件已作废，无法进行操作!')
          return
        } else {
          if (isChecked) {
            let ids = []
            list.forEach((element) => {
              ids.push(element.id)
            })
            let data = {
              ids: ids,
              fileStatus: e,
              operationType: 'filing',
            }
            userService
              .updateBusinessDirectory(data)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success('归档成功')
                  this.$emit('getList', this.leftCheckedNode)
                }
              })
          }
        }
      })
    },
    // 发布
    publish (e, list) {

      // let isChecked = list.every((val, index, arr) => {
      //   return arr[index].docType == 'directory' ? false : true
      // })

	  let isChecked = list.every((val, index, arr) => {
	    return arr[index].docType == 'directory' ? false : true
	  })
	  let isDirectoryChecked = list.every((val, index, arr) => {
	    return arr[index].docType != 'directory' ? false : true
	  })
	  console.log(isChecked,isDirectoryChecked,list,'listlistlist')
	  if(!isChecked&&!isDirectoryChecked){
		this.$message.info('不同文件类型无法操作')
		return
	  }
      // if (!isChecked) {
      //   this.$message.info('文件夹无法操作')
      //   return
      // }
	  //文件
	  // if(isChecked&&list.length>=1){
	  // 	this.$message.info('文件夹选择多个无法操作')
	  // 	return
	  // }
      this.$confirm(
        '发布之后将无法删除文件，是否确认发布选中文件？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        let fileState = list.some((val, index, arr) => {
          return arr[index].fileStatus == '4' ? false : false
        })
        if (fileState) {
          this.$message.info('选中的文件已作废，无法进行操作!')
          return
        } else {
          if (isChecked) {
            let ids = []
            list.forEach((element) => {
              ids.push(element.id)
              // queryFilesKnoledge([element],this.embed_model,false)
            })

            let data = {
              ids: ids,
              fileStatus: e,
              operationType: 'publish',
            }


            userService
              .updateBusinessDirectory(data)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success('发布成功')
                  this.$emit('getList', this.leftCheckedNode)

                }
              })
          }else{
			  let params = []
			  list.forEach((item) => {
			    params.push({
			  	type:'business',
			  	resultName:'',
          resultParent:item.id,//自己id
          mount:this.leftCheckedNode.businessDirectoryId,//父id
			    })
			  })
			  userService
			  	  .updatePublishFolder(params)
			  	  .then((res) => {
			  	    if (res.code === 200) {
			  	      this.$message.success('发布成功')
			  	      this.$emit('getList', this.leftCheckedNode)

			  	    }
			  })
		  }
        }
      })
    },
    // 作废
    invalid (e, list) {
      let isChecked = list.every((val, index, arr) => {
        return arr[index].docType == 'directory' ? false : true
      })
      if (!isChecked) {
        this.$message.info('文件夹无法操作')
        return
      }
      this.$confirm(
        '作废之后文件无效,无法进行操作,是否确认作废选中文件？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        let fileState = list.some((val, index, arr) => {
          return arr[index].fileStatus == '4' ? true : false
        })
        if (fileState) {
          this.$message.info('选中的文件已作废，无法进行操作!')
          return
        } else {
          if (isChecked) {
            let ids = []
            list.forEach((element) => {
              ids.push(element.id)
            })
            let data = {
              ids: ids,
              fileStatus: e,
              operationType: 'invalid',
            }
            userService
              .updateBusinessDirectory(data)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success('作废成功')
                  this.$emit('getList', this.leftCheckedNode)
                }
              })
          }
        }
      })
    },
    //打包下载
    download(e, list){
      let params={
        userId: this.$store.state.user.userId,
        resultParent:this.leftCheckedNode.id,
        businessDirectoryId:this.leftCheckedNode.businessDirectoryId,
        ids:list.map(item=>{
          return item.docId.toString()
        }).join(',')
      }
      userService
        .packageDownloadDirectory(params)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success('打包下载成功')
            this.$emit('getList', this.leftCheckedNode)
          }
        })
    },

    closeInvalid () {
      this.showInvalid = false
      this.invalidReason = ''
    },
    submitInvalid () { },
    // 过滤节点
    filterNode (value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 新建文件夹- 获取点击权限组织树结构的数据
    getOrganizationTree (data) {
      this.deptId = []
      this.deptName = []
      this.$refs.tree.getCheckedNodes().forEach((element) => {
        this.deptId.push(element.id)
        this.deptName.push(element.label)
      })
    },
    // 递归遍历树形结构
    mapTree (org) {
      let deptId = this.$store.state.user.deptId
      const haveChildren =
        Array.isArray(org.children) && org.children.length > 0
      return {
        //分别将我们查询出来的值做出改变他的key
        label: org.label,
        disabled: Boolean(deptId == org.id),
        id: org.id,
        //判断它是否存在子集，若果存在就进行再次进行遍历操作，直到不存在子集便对其他的元素进行操作
        children: haveChildren
          ? org.children.map((i) => this.mapTree(i))
          : null,
      }
    },
    mapTree1(org) {
      // 如果 type 为 0 且 children 为空，则返回 null 表示过滤掉该节点
      if (org.type === '0' && (org.children === null || org.children.length === 0) ) {
        return null;
      }
      let postIds = this.$store.state.user.postIds;
      const haveChildren = Array.isArray(org.children) && org.children.length > 0;

      const node = {
        label: org.isLeaf === 0 ? org.label + '(部门)' : (org.isLeaf === 1 ? org.label + '(岗位)' : org.label + '(人员)'),
        disabled: Boolean(postIds.includes(org.id)),
        range:  org.isLeaf === 0 ? 'department' :'post' ,
        isLeaf: org.isLeaf,
        type: org.type,
        id: org.id,
        children: haveChildren ? org.children.map((i) => this.mapTree1(i)).filter(Boolean) : null,
      };
      return node;
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 30px 20px 0;
}
</style>
