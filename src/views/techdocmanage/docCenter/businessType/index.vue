<template>
	<div class="wrap">
		<div id="content-left" class="content-left" ref="leftDom"
			:class="{ 'content-left--collapsed': !leftPanelVisible }"
			:style="{ width: leftPanelVisible ? '220px' : '0px' }">
			<div class="handle-tools">
				<div @click="newFolder" v-hasPermi="['techDocManage:result:BusinessDirectory']">
					<el-tooltip effect="dark" content="新建文件夹" placement="top">
						<svg-icon icon-class="材料新建icon" class="svgIcon" />
					</el-tooltip>
				</div>
				<div @click="editFolder" v-hasPermi="['techDocManage:modifyFileName']">
					<el-tooltip effect="dark" content="修改" placement="top">
						<svg-icon icon-class="材料重命名icon" class="svgIcon" />
					</el-tooltip>
				</div>
				<div @click="deleteFile(true)" v-hasPermi="['techDocManage:fileDeletion']">
					<el-tooltip effect="dark" content="删除" placement="top">
						<svg-icon icon-class="材料删除icon" class="svgIcon" />
					</el-tooltip>
				</div>

			</div>
			<div class="tree" @click="cancleAllClickNode">
				<el-tree ref="tree" node-key="id" highlight-current :data="treeData" :auto-expand-parent="false" :props="defaultProps"
					:default-expanded-keys="expanKeys" :expand-on-click-node="false" @node-click="handleNodeClick"
					@node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" @node-drop="handleDrop"
					:allow-drop="allowDrop" :allow-drag="allowDrag" draggable>
					<span class="tree-node" slot-scope="{ node }" :title="node.label">
						<img src="@/assets/docCenter/folder.png" class="tree-node__icon" />
						<span>{{ node.label }}</span>
						<!-- <span>{{ "(" + data.count + ")" }}</span> -->
					</span>
				</el-tree>
			</div>
			<div class="recycle-bin"  :class="selectLeftType=='0' ? 'actBG' : ''"
				@click="changeTable('0')">
				<i class="el-icon-download"></i>下载记录
			</div>
			<div class="recycle-bin" v-hasPermi="['techDocManage:recycleBin']" :class="selectLeftType=='1' ? 'actBG' : ''"
				@click="changeTable('1')">
				<i class="el-icon-delete"></i>回收站
			</div>
			<div class="move-line" ref="moveDom" v-show="leftPanelVisible"></div>
		</div>
		<div class="map-toggle-out" :class="{ 'map-toggle-out--collapsed': !leftPanelVisible }">
			<i id="inOutButton" :class="leftPanelVisible ? 'el-icon-d-arrow-left' : 'el-icon-d-arrow-right'"
				@click="toggle"></i>
		</div>
		<div class="content-right" :class="{ 'content-right--expanded': !leftPanelVisible }">
			<div class="content-right__top">
				<tools-line :btns="filteredBtns()" :sort-list="sortList" :show-btns="showBtns" :showTools="showTools"
					:show-upload="!selectLeftType" :crumbs-data="crumbsData" :more-data="selectLeftType ? [] : moreData"
					:collectEvent="collectEvent" :permission="permission" @btnClick="btnClick" @uploadClick="uploadFile"
					@radioClick="radioClick" @crumbsBack="breadcrumbBack" @moreClick="moreClick"
					@change="sortByChange"></tools-line>
			</div>
			<div class="content-right__con" style="position: relative">
				<file-table style="position: absolute; width: 100%" ref="fileTable" :btns="btnList[tableStyle]"
					:table-data="tableData" :isCardPlay="isCardPlay" :showTools="showTools" :treeData="treeData"
					:leftCheckedNode="leftCheckedNode" :leftCheckedNodeId="leftCheckedNodeId"
					:selectLeftType="selectLeftType"
					:collectEvent="collectEvent" @getTree="getTree" @setTreeNode="setTreeNode"
					@tableDataList="tableDataList" @getList="getList" @previewFile="previewFile" @clearFile="clearFile"
					@recoverFile="recoverFile" @deleteFile="deleteFile" @moveFile="moveFile" @shareFile="shareFile"
					@downloadFile="downloadFile" @selectionChange="selectionChange" @goInFolder="goInFolder"
					@fileChat="fileChat" @aiReviewDraft="aiReviewDraft" />
				<pagination style="position: absolute; right: 0; bottom: 0" v-show="total > 0" :total="total"
					:page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
			</div>
		</div>

		<tree-handle ref="treeHandle" :leftCheckedNodeId="leftCheckedNodeId" :leftCheckedNode="leftCheckedNode"
			:checkedNode="checkedNode" @getTree="getTree" @getList="getList"></tree-handle>
	</div>
</template>

<script>
	/**
	 * 表格类型，不同类型表示不同的按钮组
	 * 0——文件——收藏0、下载1、分享2、移动3、签出4、删除5、详细信息6
	 * 1———回收站——还原0、彻底删除1
	 */
	import axios from "axios";
	import userService from "@/api/techdocmanage/docCenter/user";
	import fileTable from "./fileTable.vue";
	import treeHandle from "./treeHandle.vue";
	import toolsLine from "../toolsLine.vue";
	import {
		listDocumentUploadTask,
		delDocumentDownloadTask,
		addShareDoc
	} from "@/api/techdocmanage/docUploadTask";
	import {
		queryFilesKnoledge
	} from "../uploadFileChat.js";
	let defaultMoreData=[
					{
						label: "权限设定",
						value: 1
					},
					{
						label: "归档",
						value: 2
					},
					{
						label: "发布",
						value: 3
					},
					{
						label: "作废",
						value: 4
					},
					{
						label: "打包下载",
						value: 5
					},
				]
	
	
	const sortByList = {
		0: "名称",
		1: "大小",
		2: "上传者",
		3: "上传时间",
	};
	export default {
		name: "BusinessType",
		components: {
			fileTable,
			treeHandle,
			toolsLine
		},
		data() {
			return {
				typeName: true,
				leftPanelVisible: true, // 控制左侧面板显示/隐藏
				leftDom: null,
				clientStartX: 0,
				collectEvent: "",
				defaultProps: {
					children: "children",
					label: "treeName",
					isLeaf: "folderFlag",
				},
				treeData: [],
				expanKeys: [],
				leftCheckedNodeId: null,
				leftCheckedNode: null,
				checkedNode: null,
				tableStyle: 0,
				btnList: [
					[{
							label: "详细信息",
							type: null,
							value: 6,
							src: require("@/assets/docCenter/detail_info.png"),
							src2: require("@/assets/docCenter/detail_info.png"),
							icon: "el-icon-info",
							permi: ["techDocManage:result:query"],
							disabled: false,
						},
						{
							label: "收藏",
							type: "warning",
							value: 0,
							src: require("@/assets/docCenter/collect.png"),
							src2: require("@/assets/docCenter/collect_full.png"),
							icon: "el-icon-star-off",
							permi: ["techDocManage:collect:add"],
							disabled: false,
						},
						{
							label: "下载",
							type: "info",
							value: 1,
							src: require("@/assets/docCenter/download.png"),
							src2: require("@/assets/docCenter/download.png"),
							icon: "el-icon-download",
							permi: ["techDocManage:document:download"],
							disabled: false,
						},
						{
							label: "分享",
							type: "success",
							value: 2,
							src: require("@/assets/docCenter/share.png"),
							src2: require("@/assets/docCenter/share.png"),
							icon: "el-icon-share",
							permi: ["techDocManage:docShare:add"],
							disabled: false,
						},
						{
							label: "移动",
							type: "primary",
							value: 3,
							src: require("@/assets/docCenter/move.png"),
							src2: require("@/assets/docCenter/move.png"),
							icon: "el-icon-rank",
							permi: ["techDocManage:result:moveFile"],
							disabled: false,
						},
						// {
						//     label: '签出',
						//     label2: '签入',
						//     type: null,
						//     value: 4,
						//     src: require('@/assets/docCenter/checkout.png'),
						//     src2: require('@/assets/docCenter/checkin.png'),
						//     icon: 'el-icon-d-arrow-right',
						//     permi: ['techdocmanage:result:moveOutFile'],
						//     id: 123,
						//     // disabled: true,
						// },
						{
							label: "删除",
							type: "danger",
							value: 5,
							src: require("@/assets/docCenter/delete.png"),
							src2: require("@/assets/docCenter/delete.png"),
							icon: "el-icon-delete",
							permi: ["techDocManage:document:delete"],
							disabled: false,
						},

						{
							label: "编辑",
							type: null,
							value: 7,
							src: require("@/assets/docCenter/modify.png"),
							src2: require("@/assets/docCenter/modify.png"),
							icon: "el-icon-edit-outline",
							permi: ["techDocManage:result:edit"],
							disabled: false,
						},
						{
							label: "标签设置",
							type: "info",
							value: 8,
							src: require("@/assets/docCenter/detail.png"),
							src2: require("@/assets/docCenter/detail.png"),
							icon: "el-icon-setting",
							permi: ["techDocManage:result:edit"],
							disabled: false,
						},
						{
							label: "文件对话",
							type: "primary",
							value: 99,
							src: require("@/assets/docCenter/file.png"),
							src2: require("@/assets/docCenter/file.png"),
							icon: "el-icon-edit-outline",
							permi: ["techDocManage:result:edit"],
							disabled: false,
						},
						{
							label: "查看权限",
							type: "primary",
							value: 89,
							src: require("@/assets/docCenter/查看权限.png"),
							src2: require("@/assets/docCenter/查看权限.png"),
							icon: "el-icon-edit-outline",
							permi: ["techDocManage:result:edit"],
							disabled: false,
						},
						{
							label: "智能修订",
							type: "null",
							value: 101,
							src: require("@/assets/docCenter/核稿.png"),
							src2: require("@/assets/docCenter/核稿.png"),
							icon: "el-icon-tickets",
							permi: ["techDocManage:result:edit"],
							disabled: false,
						},
						// ,{
						//   label: '文件对话',
						//   type: 'primary',
						//   value: 99,
						//   src: require('@/assets/docCenter/detail_info.png'),
						//   src2: require('@/assets/docCenter/detail_info.png'),
						//   icon: 'el-icon-info',
						// },
					],
					[{
							label: "还原",
							type: "primary",
							value: 0,
							src: require("@/assets/docCenter/restore.png"),
							src2: require("@/assets/docCenter/restore.png"),
							icon: "el-icon-refresh-left",
							permi: ["techDocManage:document:restore"],
							disabled: false,
						},
						{
							label: "彻底删除",
							type: "danger",
							value: 1,
							src: require("@/assets/docCenter/delete.png"),
							src2: require("@/assets/docCenter/delete.png"),
							icon: "el-icon-delete",
							permi: ["techDocManage:result:businessRecycleClear"],
							disabled: false,
						},
					],
				],
				moreData: [],
				sortList: [{
						label: "名称",
						value: 0
					},
					{
						label: "大小",
						value: 1
					},
					{
						label: "上传人",
						value: 2
					},
					{
						label: "上传时间",
						value: 3
					},
				],
				crumbsData: [],
				showBtns: false,
				isCardPlay: true,
				showTools: true,
				selectLeftType: null,
				tableData: [],
				parentId: "",
				total: 0,
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					type: "business",
				},
				// 排序
				sortData: {},
				// 回收站列表状态
				recycleStatus: false,
				// 权限标识
				permission: true,
				// 聊天文件列表
				fileChatList: [],
				embed_model: "",
				nodeExpandId: [],
				excludeArr:[]
			};
		},
		created () {
			this.moreData=JSON.parse(JSON.stringify(defaultMoreData))
		  // // 控制是否屏蔽全局console.log 日志；isDebug设为false即可屏蔽
		  // const isDebug = false;
		  // console.log = (function (oldLogFunc) {
		  //   return function () {
		  //     if (isDebug) {
		  //       oldLogFunc.apply(this, arguments);
		  //     }
		  //   };
		  // })(console.log);;
		},
		mounted() {
			// 移除jQuery事件绑定，使用Vue的@click事件
			this.getConfigKey("embed_model").then((response) => {
				this.embed_model = response.msg;
			});
			//
			/** 左侧树拖拽 */
			this.leftDom = this.$refs.leftDom;
			let moveDom = this.$refs.moveDom;
			moveDom.onmousedown = (e) => {
				this.clientStartX = e.clientX;
				e.preventDefault();

				document.onmousemove = (e) => {
					this.moveHandle(e.clientX, this.leftDom);
				};

				document.onmouseup = (e) => {
					document.onmouseup = null;
					document.onmousemove = null;
				};
			};
			/** 监听可视区变化 */
			if (document.body.clientWidth < 1200) {
				this.showTools = false;
			}
			window.onresize = () => {
				return (() => {
					if (document.body.clientWidth < 1200) {
						this.showTools = false;
					} else {
						this.showTools = true;
					}
				})();
			};
			/** 初始化树结构 */
			console.log(22222222222)
			this.getTree("init");
		},
		watch: {
			btnList: {
				handler(val) {},
				immediate: true,
				deep: true,
			},
			leftPanelVisible: {
				handler(newVal) {
					// 延迟触发resize事件，确保布局动画完成后重新计算
					setTimeout(() => {
						window.dispatchEvent(new Event('resize'));
					}, 350);
				}
			}
		},
		methods: {
			// 过滤按钮数据，移除收藏、分享、移动、智能修订
			filteredBtns() {
				// 普通模式下过滤掉指定的按钮
				const excludeValues = this.excludeArr.length > 0 ? this.excludeArr: [0, 2, 3, 101]; // 收藏、分享、移动、智能修订
				return this.btnList[this.tableStyle].filter(btn => !excludeValues.includes(btn.value));
			},
			toggle() {
				this.leftPanelVisible = !this.leftPanelVisible;
				// 延迟触发表格重新计算布局
				this.$nextTick(() => {
					setTimeout(() => {
						if (this.$refs.fileTable && this.$refs.fileTable.$refs.fileTable) {
							this.$refs.fileTable.$refs.fileTable.doLayout();
						}
					}, 350); // 等待动画完成
				});
			},
			// 排序方法
			sortByChange(e) {
				if (e.flag == 4) {
					// 回收站排序
					this.queryParams.order = e.sortDesc ? "asc" : "desc";
					this.queryParams.userDocName = e.inputValue;
					if (this.queryParams.resultName) delete this.queryParams.resultName
					let sort;
					let str =
						"doc_document_result.invalid_time is null,doc_document_result.invalid_time asc,";
					if (e.sortBy === 0) {
						this.queryParams.sort = str + "doc_document_result.result_name";
					} else if (e.sortBy === 1) {
						this.queryParams.sort = str + "doc_document.doc_size";
					} else if (e.sortBy === 2) {
						this.queryParams.sort = str + "doc_document.delete_id";
					} else if (e.sortBy === 3) {
						this.queryParams.sort = str + "doc_document.delete_time";
					}
					userService.recycleList(this.queryParams).then((res) => {
						if (res.code === 200) {
							this.total = res.total;
							this.tableData = res.rows;
						}
					});
				} else {
					// 列表排序
					this.queryParams.order = e.sortDesc ? "asc" : "desc";
					this.queryParams.resultName = e.inputValue;
					let sort;
					let str =
						"doc_document_result.invalid_time is null,doc_document_result.invalid_time asc,";
					if (e.sortBy === 0) {
						this.queryParams.sort = str + "doc_document_result.result_name";
					} else if (e.sortBy === 1) {
						this.queryParams.sort = str + "doc_document.doc_size";
					} else if (e.sortBy === 2) {
						this.queryParams.sort = str + "doc_document_result.create_id";
					} else if (e.sortBy === 3) {
						this.queryParams.sort = str + "doc_document_result.create_time";
					}
					let data = this.queryParams;
					userService.qList(data).then((res) => {
						this.total = res.total;
						this.tableData = res.rows;
					});
				}
			},
			/** 触发组件按钮事件 */
			btnClick(e) {
				this.$refs.fileTable.filetableHandle(e);
			},

			// 文档下载
			downloadFile(list) {
				let fileName = list.resultName.substr(
					0,
					list.resultName.lastIndexOf(".")
				);
				let suffix = list.resultName.substr(list.resultName.lastIndexOf(".") + 1);
				let lists = [];
				lists.push(list);
				let id = lists[0].docId.toString();
				let data = {
					id: lists[0].docId.toString(),
					userId: this.$store.state.user.userId,
				};
				userService.downloadFile(data).then((res) => {
					let blob = new Blob([res]);
					blob.text().then((result) => {
						try {
							let res = JSON.parse(result);
							if (res.code === 500) {
								this.$message.error(res.msg);
								return;
							}
						} catch (error) {
							let objectUrl = URL.createObjectURL(blob);
							let link = document.createElement("a");
							// if (suffix !== 'jpg') {
							// pdf下载
							link.download = list.resultName;
							// } else {
							//   // 源文件下载
							//   link.download = list.resultName
							// }
							link.href = objectUrl;
							link.click();
							link.remove();
						}
					});
				});
			},
			// 文档分享
			shareFile(data) {
				
			},
			// 文档移动
			moveFile(data) {
				userService.moveFile(data).then((res) => {
					if (res.code === 200) {
						this.$message.success("移动成功");
						this.getTree();
						this.getList(this.leftCheckedNode);
					}
				});
			},
			radioClick(e) {
				this.isCardPlay = e === "摘要" ? true : false;
			},
			/** 新建文件夹 */
			newFolder() {
				if (!this.leftCheckedNodeId) return this.$message.info("请选择目录");
				if (
					(this.leftCheckedNode.childIsFolder &&
						this.leftCheckedNode.type == "business") ||
					this.leftCheckedNode.type == "directory"
				) {
					this.$refs.treeHandle.openFolder();
				} else {
					this.$message.info("请在最后一级目录下创建文件夹");
				}
			},
			/** 修改文件夹 */
			editFolder() {
				let clickNode = this.$refs.tree.getCurrentNode();
        const node = this.$refs.tree.getNode(clickNode.id)
        this.$refs.treeHandle.editFolderFuc(clickNode,node);
			},
			/** 删除, e: true左侧删除 false右侧删除 */
			deleteFile(e, id) {
				if (e) {
					console.log(1111111111, e);
					let clickNode = this.$refs.tree.getCurrentNode();
					if (!clickNode) {
						this.$message.warning("请选择文件");
						return;
					}
					if (clickNode.type == "business" || clickNode.id == "file") {
						this.$message.warning("不可删除目录");
						return;
					}
					this.$confirm(
						"删除文件夹会使该文件夹下所有文件都被删除，是否确定删除？",
						"提示", {
							confirmButtonText: "删除",
							cancelButtonText: "取消",
							type: "warning",
						}
					).then(() => {
						if (clickNode.type === "directory") {
							userService.deleteDirectory(clickNode.id).then((res) => {
								this.$message.success("文件夹删除成功");
								this.getTree();
							});
						}
					});
				} else {
					if(this.selectLeftType=='0'){
						this.$confirm("是否删除下载文件？", "提示", {
							confirmButtonText: "删除",
							cancelButtonText: "取消",
							type: "warning",
						}).then(() => {
							delDocumentDownloadTask(id).then((res) => {
							   this.$message.success("文件任务删除成功");
							   this.changeTable(0)
							});
						});
						
					}else{
						this.$confirm("是否删除选中的文件或文件夹？", "提示", {
							confirmButtonText: "删除",
							cancelButtonText: "取消",
							type: "warning",
						}).then(() => {
							userService.deleteDirectory(id).then((res) => {
								if (res.code === 200) {
									this.$message.success("文件删除成功");
									this.getList(this.leftCheckedNode);
									this.getTree();
								}
							});
							
						});
					}
					
				}
			},
			/** 更多操作 */
			moreClick(e) {
				this.$refs.fileTable.handleCommand(e);
			},
			/** 上传 */
			uploadFile() {
				if (!this.leftCheckedNodeId)
					return this.$message.info("目录不能上传文件，请选择文件夹");
				if (this.leftCheckedNode.docType == "directory") {
					this.$refs.treeHandle.showUpload = true;
				} else {
					this.$message.info("目录不能上传文件，请选择文件夹");
				}
			},
			// 聊天对话
			fileChat(val) {
				const data = val?.length && val[0] ? val : this.fileChatList;

				queryFilesKnoledge(data, this.embed_model);
			},
			// 智能核稿
			async aiReviewDraft(val) {
				console.log(this.fileChatList);
				console.log(val);
				const result = val ? val : this.fileChatList;
				if (val) {
					let params = {
						resultId: result.id,
						docId: result.docId,
					};
					const {
						code,
						data
					} = await userService.aiReviewDraft(params);
					if (code === 200) {
						this.$message.success("已产生修订记录");
						this.$router.push("/techmanage/smartGenerate/formation");
					}
				} else {
					if (result[0].locked == 0) {
						this.$message.success("正在修订中");
					} else {
						if (this.fileChatList.length == 1) {
							let params = {
								resultId: result[0].id,
								docId: result[0].docId,
							};
							console.log(params);
							const {
								code,
								data
							} = await userService.aiReviewDraft(params);
							if (code === 200) {
								this.$message.success("已产生修订记录");
								this.$router.push("/techmanage/smartGenerate/formation");
							}
						} else {
							this.$message.info("请选择一个文件");
							return;
						}
					}
				}
			},
		   selectionChange(list) {
			this.fileChatList = list;
			
			if (list.length == 0) {
				// 没有选择任何文件时，重置所有按钮状态
				this.excludeArr = [];
				this.moreData = JSON.parse(JSON.stringify(defaultMoreData));
				this.showBtns = false;
				return;
			}

			this.showBtns = true;

			// 判断选择的文件类型
			const hasFolder = list.some(item => item.docType === "directory");
			const hasFile = list.some(item => item.docType === "file");

			if (hasFolder && hasFile) {
				// 情况2：选择既有文件夹又有文件时，只显示上传
				this.excludeArr = [0, 1, 2, 3, 5, 6, 7, 8, 89, 99, 101]; // 排除除上传外的所有按钮
				this.moreData = []; // 清空更多操作
			} else if (hasFolder && !hasFile) {
				// 情况1：选择文件夹时，只显示上传、删除
				this.excludeArr = [0, 1, 2, 3, 6, 7, 8, 89, 99, 101]; // 排除收藏、下载、分享、移动、详细信息、编辑、标签设置、查看权限、文件对话、智能修订
				this.moreData = [
				{
					label: "发布",
					value: 3
				},
				];
			} else if (!hasFolder && hasFile) {
				// 情况3：选择只有文件时，显示下载、删除、标签设置、文件对话、权限设定、归档、发布、作废
				this.excludeArr = [0, 2, 3, 6, 7, 89, 101]; // 排除收藏、分享、移动、详细信息、编辑、查看权限、智能修订
				this.moreData = [
				{
					label: "权限设定",
					value: 1
				},
				{
					label: "归档", 
					value: 2
				},
				{
					label: "发布",
					value: 3
				},
				{
					label: "作废",
					value: 4
				}
				];
			}

			// 处理权限相关的按钮禁用逻辑（保留原有的权限控制逻辑）
			if (list.length === 1) {
				this.handleSingleFilePermission(list[0]);
			} else {
				this.handleMultipleFilesPermission(list);
			}
			},

			// 处理单个文件的权限逻辑
			handleSingleFilePermission(item) {
			if (item.authOperation == 1) {
				this.disableButtons(["下载", "分享", "收藏"], true);
			} else if (item.authOperation == 2) {
				this.disableButtons(["下载"], false);
				this.disableButtons(["分享", "收藏"], true);
			} else if (item.authOperation == 3) {
				this.disableButtons(["下载", "收藏"], false);
				this.disableButtons(["分享"], true);
			} else if (item.authOperation == 4) {
				this.disableButtons(["下载", "分享", "收藏"], false);
			}
			},

			// 处理多个文件的权限逻辑
			handleMultipleFilesPermission(list) {
			const authOperations = list.map(item => item.authOperation);
			const minAuth = Math.min(...authOperations);
			
			if (minAuth == 1) {
				this.disableButtons(["下载", "分享", "收藏"], true);
			} else if (minAuth == 2) {
				this.disableButtons(["下载"], false);
				this.disableButtons(["分享", "收藏"], true);
			} else if (minAuth == 3) {
				this.disableButtons(["下载", "收藏"], false);
				this.disableButtons(["分享"], true);
			} else if (minAuth == 4) {
				this.disableButtons(["下载", "分享", "收藏"], false);
			}
			},

			// 辅助方法：设置按钮禁用状态
			disableButtons(buttonLabels, disabled) {
			this.btnList[0].forEach((item, index) => {
				if (buttonLabels.includes(item.label)) {
				this.btnList[0][index].disabled = disabled;
				}
			});
			},
			/** 表格类型变换(左菜单点击事件) */
			changeTable(e) {
				console.log(this.isCardPlay,'22222')
				this.$refs.tree.setCurrentKey(null);
				this.queryParams.pageNum = 1;
				this.recycleStatus = true;
				this.showBtns = false;
				this.tableStyle = e;
				delete this.queryParams.mount;
				delete this.queryParams.resultParent;
				delete this.queryParams.type;
				this.queryParams.userDocName = this.queryParams.resultName
				delete this.queryParams.resultName
				this.crumbsData.splice(0, this.crumbsData.length);
				this.excludeArr=[]
				this.selectLeftType = e;
				if (e == 1) {
					this.collectEvent = 4;
					userService.recycleList(this.queryParams).then((res) => {
						if (res.code === 200) {
							this.total = res.total;
							this.tableData = res.rows;
						}
					});
				}else if (e==0){
					this.collectEvent = false;
					this.excludeArr=[0,1,2, 3,8,99, 101]
					let obj={...this.queryParams,taskType:"批量下载"}
					listDocumentUploadTask(obj).then((res) => {
						if (res.code === 200) {
							this.total = res.total;
							this.tableData = res.rows;
						}
					});
				}
				
			},
			// 回收站恢复
			recoverFile(id) {
				userService.recoverFile(id).then((res) => {
					if (res.code === 200) {
						this.$message.success("还原成功");
						this.changeTable(1);
						this.getTree();
					}
				});
			},
			// 回收站清空
			clearFile(id) {
				userService.clearFile(id).then((res) => {
					if (res.code === 200) {
						this.$message.success("删除成功");
						this.changeTable(1);
					}
				});
			},
			/** 调整左侧div宽度 */
			moveHandle(nowClientX, leftDom) {
				let computedX = nowClientX - this.clientStartX;
				let leftBoxWidth = parseInt(leftDom.style.width);
				let changeWidth = leftBoxWidth + computedX;
				if (changeWidth < 225) {
					changeWidth = 225;
				}
				if (changeWidth > 400) {
					changeWidth = 400;
				}
				leftDom.style.width = changeWidth + "px";
				this.clientStartX = nowClientX;
			},
			/** 左侧树展开事件 */
			handleNodeExpand(data) {
				this.expanKeys.push(data.id);
				console.log("展开节点: ", this.expanKeys)
			},
			// 移除收缩的节点
			handleNodeCollapse(data) {
				const index = this.expanKeys.indexOf(data.id);
				if (index > -1) {
					this.expanKeys.splice(index, 1);
				}
				console.log("收缩节点: ", this.expanKeys)
			},
			handleDrop(draggingNode, dropNode, dropType, ev) {
				let obj = {
					id: dropNode.parent.data.id,
					children: dropNode.parent.data.children.map(item => {
						return {
							id: item.id
						}
					})
				}
				userService.updateDirectoryTree(obj).then(res=>{
					this.handleNodeClick(dropNode.parent.data,dropNode.parent)
				})
			},
      allowDrop(moveNode, inNode, type) {
        // 1. 基础校验：确保拖拽节点和目标节点是兄弟节点（同一父节点）
        if (moveNode.parent.id !== inNode.parent.id) {
          return false;
        }

        // 2. 防止拖拽到自身
        if (moveNode === inNode) {
          return false;
        }

        // 3. 同级节点排序逻辑（适用于所有层级）
        if (moveNode.level === inNode.level) {
          // 处理边界情况：
          // - 如果是列表最后一个节点，只允许插入到前一个位置（type='prev'）
          // - 如果是列表第一个节点，只允许插入到后一个位置（type='next'）
          if (!moveNode.nextSibling) return type === 'prev';
          if (!inNode.nextSibling) return type === 'next';

          // 常规情况：根据相邻关系判断
          return moveNode.nextSibling.id === inNode.id
            ? type === 'next'
            : type === 'prev';
        }

        // 4. （可选）如果需要支持跨层级拖拽，可在此添加额外逻辑
        // 例如：允许子节点拖到兄弟节点之后
        // if (moveNode.level === inNode.level + 1 && type === 'next') {
        //   return true;
        // }

        return false;
      },
			allowDrag(draggingNode) {
				return draggingNode.data.businessDirectoryId;
			},
			/** 左侧树点击事件 */
			handleNodeClick(data, node) {
				
				this.recycleStatus = false;
				this.queryParams.pageNum = 1;
				this.selectLeftType = null;
				this.tableStyle = 0;
				this.excludeArr=[]
				if (data.folderFlag == 0) {
					this.getList(data.parentId);
					return;
				}
				this.collectEvent = false;
				this.leftCheckedNodeId = data.id;
				this.leftCheckedNode = data;
				this.checkedNode = node;
				// if (data.parentId != 0) {
				// this.expanKeys = [data.id]; //左边树节点展开
				this.getList(data);
				// }
				this.crumbsData = this.getParent(data);
				console.log(this.isCardPlay,'22222')
			},
			/** 点击空白处取消树节点选择 */
			cancleAllClickNode() {
				// this.getList(0)
				// this.selectLeftType = false
				// this.getTree()
				// this.tableStyle = 0
				this.$refs.tree.setCurrentKey(null);
			},
			/** 右侧文件夹点击事件 */
			goInFolder(data) {
				this.getList(data); //进入文件夹
				this.expanKeys = [data.id]; //左边树节点展开
				this.$refs.tree.setCurrentKey(data.id); //左边树节点选中
				this.crumbsData = this.getParent(data); //面包屑重载
			},
			// 右侧文件点击预览
			previewFile(row) {
				this.$router.push({
					name: "previewPdf",
					params: {
						docId: row.docId
					}
				});
				// 去掉pdf.js初始关键字搜索
				this.$store.commit("common/PDFDOCKEYWORD", null);
			},
			/** 面包屑-递归查找父结构 */
			getParent(data) {
				const treeFindPath = (tree, subNode, path = []) => {
					if (!tree) return [];
					for (const data of tree) {
						path.push({
							name: data.treeName,
							id: data.id,
							parentId: data.resultParent || 0,
							type: data.type,
							catalogueId: data.businessDirectoryId,
						});
						if (data.id === subNode.id) return path;
						if (data.children) {
							const findChildren = treeFindPath(data.children, subNode, path);
							if (findChildren.length) return findChildren;
						}
						path.pop();
					}
					return [];
				};
				let result = treeFindPath(this.treeData, data);
				return result;
			},
			/** 面包屑返回点击事件 */
			breadcrumbBack() {
				let curitem = this.crumbsData.length ?
					this.crumbsData[this.crumbsData.length - 1] :
					false;
				if (curitem) {
					let data = {
						id: curitem.parentId,
						businessDirectoryId: curitem.catalogueId,
						type: curitem.type,
					};

					this.tableDataList(data);
					//进入文件夹
					let parentId;
					if (curitem.parentId == -1) {
						parentId = curitem.catalogueId;
					} else {
						parentId = curitem.parentId;
					}
					this.crumbsData = this.getParent({
						id: parentId
					}); //面包屑重载
					if (this.crumbsData.length === 0) {
						this.$refs.tree.setCurrentKey(null); //左边树节点选中
					} else {
						if (curitem.parentId == -1) {
							this.$refs.tree.setCurrentKey(curitem.catalogueId);
						} else if (curitem.parentId == 0) {
							this.$refs.tree.setCurrentKey(null); //左边树节点选中
						} else {
							this.$refs.tree.setCurrentKey(curitem.parentId); //左边树节点选中
						}
					}
				}
			},
			collectIds(tree) {
				const ids = [];

				const traverse = (nodes, currentLevel = 1) => {
					nodes.forEach((node) => {
						// 只收集前2级的节点ID（第1级和第2级）
						if (currentLevel <= 1) {
							ids.push(node.id);
						}

						// 如果有子节点且当前级别小于2，递归遍历子节点
						if (node.children && node.children.length > 0 && currentLevel < 1) {
							traverse(node.children, currentLevel + 1);
						}
					});
				};

				// 开始遍历树的根节点，从第1级开始
				traverse(tree, 1);

				this.expanKeys = ids;

				// 返回包含所有id的数组
				// return ids;
			},
			/** 获取左侧树数据 */
			getTree(val) {
				// 目录索引
				let data = {
					type: "business",
				};
				userService.treeUserList(data).then((res) => {
					this.treeData = res.data;
					console.log("回调：", val);
					// this.$nextTick(() => {
					// 	this.$refs.tree.setCurrentKey(this.treeData[0].id);
					// })
					if (val == 'init') {
						this.collectIds(res.data);
					}
					// this.getList()
					console.log("this.treeData", this.expanKeys);
					// console.log("this.treeData", this.expanKeys);
				});
			},
			/** 获取表格数据 */
			getList(data) {
				console.log(this.recycleStatus,'this.recycleStatus')
				if (this.recycleStatus) {
					if (data && data.limit) {
						// 分页
						this.queryParams.pageNum = data.page;
						this.queryParams.pageSize = data.limit;
					}else if (data) {
						// 常规
						this.queryParams.pageNum = 1;
					}
					if(this.selectLeftType=='0'){
						let obj={...this.queryParams,taskType:"批量下载"}
						listDocumentUploadTask(obj).then((res) => {
							if (res.code === 200) {
								this.total = res.total;
								this.tableData = res.rows;
							}
						});
					}else{
						userService.recycleList(this.queryParams).then((res) => {
							this.total = res.total;
							this.tableData = res.rows;
						});
					}
					
				} else {
					if(this.leftCheckedNode){
						if (this.leftCheckedNode.type === "business") {
							this.queryParams.resultParent = -1;
							this.queryParams.mount = this.leftCheckedNode.id;
						} else {
							this.queryParams.resultParent = this.leftCheckedNode.id;
							this.queryParams.mount = this.leftCheckedNode.businessDirectoryId;
						}
					}
					userService.qList(this.queryParams).then((res) => {
						if (res.code === 200) {
							let superAdmin = this.$store.state.user.superAdmin;
							// if (superAdmin) {
							this.tableData = res.rows;
							// } else {
							//     let data = []
							//     res.rows.filter((item) => {
							//
							//         if (item.fileStatus == 3) {
							//             data.push(item)
							//         }
							//     })
							//
							//     this.tableData = data
							// }
							this.total = res.total;
						}
					});
				}
			},
			// 刷新列表数据
			tableDataList(data) {
				this.leftCheckedNode = data;
				this.getList();
			},
			// 双击文件夹选中左侧节点
			setTreeNode(data) {
				this.$refs.tree.setCurrentKey(data.id); //左边树节点选中
				// this.crumbsData = this.getParent(data);
				this.crumbsData = this.getParent({
					id: data.id
				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.wrap {
		display: flex;
		// height: calc(100% - 20px);
		height: calc(100vh - 84px);
		background-color: #fff;
		padding: 20px;

		.content-left {
			padding-right: 16px;
			position: relative;
			transition: width 0.35s ease;
			overflow: hidden;

			&--collapsed {
				padding-right: 0;
			}

			.handle-tools {
				display: flex;
				justify-content: space-between;
				margin-bottom: 16px;
				align-items: center;

				.svgIcon {
					cursor: pointer;
					width: 32px;
					height: 32px;
					  outline: none !important;
				}
			}

			.tree {
				height: calc(100% - 150px);
				overflow-y: auto;

				.tree-node {
					display: flex;
					align-items: center;

					&__icon {
						width: 16px;
						margin-right: 4px;
						vertical-align: middle;
					}
				}
			}

			.recycle-bin {
				height: 50px;
				line-height: 50px;
				border-top: 1px solid #dfe4ed;
				text-align: center;
				cursor: pointer;

				&:hover {
					background-color: #dfe4ed;
				}

				.pr5 {
					padding-right: 5px;
				}
			}

			.actBG {
				background-color: #dfe4ed;
				font-weight: bold;
			}

			.move-line {
				position: absolute;
				top: 0;
				height: 100%;
				left: calc(100% - 2px);
				width: 1px;
				background: #dfe4ed;
				cursor: col-resize;
			}
		}

		.content-right {
			flex: 1;
			padding-left: 16px;
			transition: padding-left 0.35s ease;
			min-width: 0; // 确保flex子项能够正确收缩

			&--expanded {
				padding-left: 5px; // 左侧面板折叠时减少左边距
			}

			&__con {
				height: calc(100% - 74px); // 调整为两行工具栏的高度：32px + 10px gap + 32px
			}
		}
	}

	::v-deep .pagination-container .el-pagination {
		top: -6px;
	}

	.map-toggle-out {
		position: relative;
		top: 45%;
		left: -20px;
		font-size: 20px;
		padding-top: 5px;
		margin-top: 10px;
		float: right;
		height: 30px;
		opacity: 0.9;
		background-color: white;
		cursor: pointer;
		transition: left 0.35s ease;

		&--collapsed {
			left: -5px;
		}
	}
</style>

