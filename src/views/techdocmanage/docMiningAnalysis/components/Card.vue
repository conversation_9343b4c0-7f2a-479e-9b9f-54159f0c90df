<template>
  <div class="card">
    <header class="title" @click="handleClick(item)">
      <span></span>
      <span class="name">{{item.docType}}({{item.docCount}})</span>

      <i class="el-icon-right" >挖掘</i>
    </header>
    <div class="content">
      <div class="item">
        <header>关键字</header>
        <ul v-if="item.keywordList.length">
          <li  v-for="(keyword,index) in item.keywordList" :key="index" @click="handleClick(item)">
            <el-tag style="width: 100%;">{{keyword.key}}({{keyword.value}})</el-tag>
          </li>
        </ul>
    <el-empty v-else  :image-size='10' style="    padding: 0;" description="暂无数据"></el-empty>
      </div>
      <div class="item">
        <header>标签</header>
        <ul v-if="item.labelList.length">
          <li v-for="(label,index) in item.labelList" :key="index" @click="handleClick(item)">
            <el-tag style="width: 100%;">{{label.key}}({{label.value}})</el-tag>
          </li>
        </ul>
            <el-empty v-else :image-size='10' style="    padding: 0;" description="暂无数据"></el-empty>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  components: {},
  props: {
    item:{
      typeof:Object,
      default:() => {}
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 卡片详情
    handleClick(val) {
      this.$router.push({
        path: "/AnalysisDetail/Detail",
        query: {
          value:val.docIds
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.card {
  box-sizing: border-box;
  width: 100%;
  min-width: 300px;
  height: 400px;
  //   background: rgba(217, 217, 217, 0.58);
  background: #fff;
  border: 1px solid white;
  box-shadow: 12px 17px 51px rgba(0, 0, 0, 0.22);
  backdrop-filter: blur(6px);
  border-radius: 17px;
  text-align: center;
  cursor: pointer;
  transition: all 0.5s;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  font-weight: bolder;
  color: black;
  display: flex;
  flex-direction: column;
  .title {
    height: 50px;
    line-height: 50px;
    width: 100%;
    background: #409eff;
    border-top-left-radius: 17px;
    border-top-right-radius: 17px;
    padding: 0 10px;
    color: #fff;
    .name {
      font-size: 16px;
      line-height: 16px;
    }
    .el-icon-right {
      float: right;
      margin-top: 20px;
    }
  }
  .content {
    flex: 1;
    width: 100%;
    overflow: hidden;
        overflow-y: auto;
    .item {
      display: flex;
      flex-direction: column;
      header {
        text-align: left;
        margin-left: 10px;
      }
      ul {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    justify-items: center;
        li {
          margin-bottom: 5px;
          text-align: left;
          // padding: 0 10px;
          font-weight: 400;
          font-size: 14px;
          height: 28px;
          line-height: 22px;
          padding: 0 10px;
        }
      }
    }
  }
}

.card:hover {
  //   border: 1px solid black;
  transform: scale(1.05);
}

.card:active {
  transform: scale(0.95) rotateZ(1.7deg);
}
ul{
      padding: 0 !important;
}
</style>
