<template>
  <div class="app-container">
    <!-- 数据过滤 -->
    <el-form
      :inline="true"
      :model="queryParams"
      label-width="68px"
      ref="queryForm"
    >
      <el-form-item label="关键字" prop="keywordName">
        <el-input
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入关键字"
          size="small"
          v-model="queryParams.keywordName"
        />
      </el-form-item>
      <el-form-item label="标签" prop="labelName">
        <el-input
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入标签"
          size="small"
          v-model="queryParams.labelName"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" icon="el-icon-search" type="primary"
          >搜索</el-button
        >
        <el-button @click="resetQuery" icon="el-icon-refresh">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 卡片列表 -->
    <div
      class="cardList"
      v-infinite-scroll="load"
      v-if="cardData.length"
      v-loading="loading"
    >
      <Card v-for="(item, index) in cardData" :key="index" :item="item" />
    </div>
    <el-empty v-else description="暂无数据"></el-empty>
  </div>
</template>

<script>
import Card from "@/views/techdocmanage/docMiningAnalysis/components/Card.vue";
import knowledgeMapService from "@/api/techdocmanage/knowledgeMap.js";
export default {
  name: "DocMiningAnalysis",
  components: { Card },
  props: {},
  data() {
    return {
      loading: false,
      queryParams: {
        labelName: null,
        keywordName: null,
      },
      cardData: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getList();
  },
  mounted() {},
  methods: {
    async getList() {
      this.loading = true;
      const { code, data } = await knowledgeMapService.getKnowledgeMineList(
        this.queryParams
      );
      if (code === 200) {
        this.cardData = data.filter((v) => v.docCount !== 0);
        this.loading = false;
      }
    },
    resetQuery() {
            this.resetForm("queryForm");
      this.handleQuery();
    },
    handleQuery() {
      this.getList()
    },
    // 触底滚动加载
    load() {
      // 
      // this.num +=5
    },
  },
};
</script>

<style scoped lang="scss">
.app-container {
  // margin: 10px;
  padding: 10px;
  border-radius: 5px;
  background: #ffffff;
  height: calc(100vh - 112px);
  .cardList {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 20px;
    justify-items: center;
    overflow: auto;
    height: calc(100% - 50px);
    padding: 10px;
  }
}
</style>
