<template>
  <div class="container">
    <el-row class="bigBox">
      <el-col :span="9" class="lBox">
        <div class="lBox__top">
          <el-button class="btnStyle" type="primary" v-hasPermi="['assistedwriting:repData:add']" @click="btn_new">新增</el-button>
          <el-button class="btnStyle" plain @click="btn_del" v-hasPermi="['assistedwriting:repData:remove']">删除</el-button>
          <el-input class="ml10" placeholder="请输入数据名称" v-model="sWord" clearable @clear="getList" @keyup.enter.native="btn_search">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="btn_search"></i>
          </el-input>
        </div>
        <el-table ref="dataTable" :data="tableData" style="width: 100%" :height="curHeight()-48" v-if="curHeight()" highlight-current-row @current-change="chgOne" @row-click="currentLine" :row-style="{height:'48px'}">
          <el-table-column prop="sort" label="序号"></el-table-column>
          <el-table-column prop="dataName" label="数据名称"/>
          <el-table-column prop="type" label="数据类型" :formatter="formatterDT"></el-table-column>
          <el-table-column align="right">
            <template slot="header" slot-scope="scope">
              <el-button class="btnStyle" type="primary" @click="btn_copy(scope.row)" v-hasPermi="['assistedwriting:repData:add']">复制</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="15" class="rBox">
        <div class="rBox__top">
          <div class="rBox__top__title"><i class="el-icon-edit pr10"></i>数据配置 : {{dataForm.id ? dataForm.dataName :'未选中'}}</div>
        </div>
        <div class="rBox__set">
          <div class="rBox__innerTitle">
            <h4><span>.</span>编辑数据</h4>
            <el-button class="btnStyle" type="primary" plain @click="btn_save" v-hasPermi="['assistedwriting:repData:addOrUpdateRepData']">保存</el-button>
          </div>
          <el-form ref="dataForm" :model="dataForm" :rules="dataForm.type=='3'?dataRules3:(dataForm.type=='2'?dataRules2:dataRules)" label-width="80px">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="数据来源" prop="dataName">
                  <el-select v-model="dataForm.dataSource" @change="changeDataSource" placeholder="请选择">
                    <el-option
                      v-for="item in sourceOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="知识库" v-if="dataForm.dataSource == 'ai' || dataForm.dataSource == 'word'" :rules="[{ required: true, message: '知识库不能为空', trigger: 'change' }]">
                  <el-select
                    v-model="dataForm.knowledgeBase"
                    placeholder="请选择知识库">
                    <el-option
                      v-for="item in knowledgeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.label"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="数据库"  v-if="dataForm.dataSource == 'database'" :rules="[{ required: true, message: '数据库不能为空', trigger: 'change' }]">
                  <el-select
                    v-model="dataForm.knowledgeBase"
                    placeholder="请选择数据库">
                    <el-option
                      v-for="source in sourceDataOptions"
                      :key="source.id"
                      :label="source.sourceName"
                      :value="source.id"
                      :disabled="source.status === '0'"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="数据名称" prop="dataName">
                  <el-input v-model="dataForm.dataName" placeholder="请输入数据名称" maxlength="30" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数据类型" prop="type" v-show="dataForm.dataSource != 'word'" >
                  <el-select v-model="dataForm.type" placeholder="请选择">
                    <el-option v-for="item in dataOptions" :key="item.dictCode" :label="item.dictLabel" :value="item.dictValue">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="向量数"  prop="vector" v-if="dataForm.dataSource == 'ai' || dataForm.dataSource == 'word'">
                  <el-input-number v-model="dataForm.vector" :min="1"  controls-position="right" placeholder="请输入向量数" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="角色"  prop="promptName" v-if="dataForm.dataSource == 'ai'">
                  <el-select
                    v-model="dataForm.promptName"
                    placeholder="请选择知识库">
                    <el-option
                      v-for="item in promptNameList"
                      :key="item.promptName"
                      :label="item.promptDescription"
                      :value="item.promptName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="序号"  prop="sort">
                  <el-input-number v-model="dataForm.sort" :min="1"  controls-position="right" placeholder="请输入序号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="文件列表" v-show="dataForm.dataSource == 'file'">
                  <el-input v-model="dataForm.sourceDocName" class="input-with-select" readonly="readonly">
                    <el-button slot="append" @click="selectFile" icon="el-icon-search"></el-button>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="文件内容" v-show="dataForm.dataSource == 'file'">
                  <el-input type="text" v-model="dataForm.contentKey" readonly="readonly"/>
                </el-form-item>
              </el-col>
              <el-col :span="dataForm.type !=3 ? 24 : 14">
                <el-form-item label="SQL参数" v-show="dataForm.dataSource == 'database'">
                  <el-input type="textarea" v-model="dataForm.sqlPara" placeholder="请输入SQL参数" maxlength="-1" rows="2" resize="none" />
                </el-form-item>
                <el-form-item label="SQL" v-show="dataForm.dataSource == 'database'">
                  <el-input @input="$forceUpdate()" type="textarea" v-model="dataForm.dataSql" placeholder="请输入SQL" maxlength="-1" rows="10" resize="none" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="问答内容" v-show="dataForm.dataSource == 'ai' || dataForm.dataSource == 'word'">
                  <el-input @input="$forceUpdate()" type="textarea" v-model="dataForm.dataSql" placeholder="" maxlength="-1" rows="10" resize="none"/>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="dataForm.type==2 ? 10 : 0">
                <el-form-item v-if="dataForm.type==2" label="表头行数" prop="line">
                  <el-input-number size="small" v-model="dataForm.line" :min="1"></el-input-number>
                </el-form-item>
                <el-form-item v-if="dataForm.type==2" label="表头列数" prop="column">
                  <el-input-number size="small" v-model="dataForm.column" :min="1"></el-input-number>
                </el-form-item>
                <el-form-item v-if="dataForm.type==2" label="表格模板" prop="filepath" style="margin-bottom:0;">
                  <el-upload
                    class="upload-demo"
                    drag
                    :action="uploadUrl"
                    :limit="1"
                    accept=".docx, .doc"
                    multiple
                    :on-success="handleFileSuccess"
                    :on-remove="handleFileRemove"
                    :before-upload="handleBeforeUpload"
                    :file-list="fileList"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    <div class="el-upload__tip" slot="tip">只能上传doc/docx格式的文件，且不超过1个</div>
                  </el-upload>
                </el-form-item>
              </el-col> -->
              <el-col :span="dataForm.type==3 ? 10 : 0">
                <el-form-item v-if="dataForm.type==3" label="X" prop="xAxis">
                  <el-input v-model="dataForm.xAxis" placeholder="请输入X" maxlength="30" />
                </el-form-item>
                <el-form-item v-if="dataForm.type==3" label="Y" prop="yAxis">
                  <el-input v-model="dataForm.yAxis" placeholder="请输入Y" maxlength="30" />
                </el-form-item>
                <el-form-item v-if="dataForm.type==3" label="图表样式" prop="chartType">
                  <el-select v-model="dataForm.chartType" placeholder="请选择">
                    <el-option v-for="item in chartTypeList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item v-if="dataForm.type==3" label="图表颜色" prop="chartcolor" style="margin-bottom:0;">
                  <div class="chart-color">
                    <div class="chart-color-box" :style="{background: chartcolor}" @click="showColorPicker=true"></div>
                    <el-input disabled v-model="dataForm.chartcolor" placeholder="点击色块选择颜色" />
                    <i class="el-icon-success ok-picker" v-show="showColorPicker" @click="showColorPicker=false"></i>

                  </div>
                  <div v-show="showColorPicker" class="chart-color-board">
                    <sketch-picker v-model="chartcolor" @input="colorValueChange"></sketch-picker>
                  </div>
                </el-form-item> -->
              </el-col>
            </el-row>
          </el-form>
          <div class="rBox__innerTitle">
            <el-button class="btnStyle" type="primary" plain @click="sqlRun"  v-if="dataForm.dataSource == 'database' && this.dataForm.knowledgeBase">SQL测试</el-button>
          </div>
        </div>
        <div class="rBox__label" v-show="showLblForm">
          <div class="rBox__innerTitle">
            <h4><span>.</span>标签配置</h4>
            <el-button class="btnStyle" type="primary" plain @click="showLbl=true,isNewLbl=true" v-hasPermi="['assistedwriting:label:add']">添加</el-button>
          </div>
          <div>
            <el-table :data="tableData2" style="width: 100%;margin:0 auto;" :row-style="{height:'48px'}">
              <el-table-column type="index" label="序号" width="60">
              </el-table-column>
              <el-table-column prop="labelName" label="标签名称" width="180">
              </el-table-column>
              <el-table-column prop="labelField" label="标签取值"></el-table-column>
              <el-table-column fixed="right" label="操作" width="180">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleEdit(scope.$index, scope.row)" v-hasPermi="['assistedwriting:label:edit']">修改</el-button>
                  <el-button type="text" @click="handleDelete(scope.$index, scope.row)" v-hasPermi="['assistedwriting:label:remove']">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog :title="isNewLbl?'新增标签':'修改标签'" :visible.sync="showLbl" width="600px" @close="closeDialog" :close-on-click-modal="false">
      <el-form ref="lblForm" :model="lblForm" :rules="lblRules" label-width="80px">
        <el-form-item label="标签名称" prop="labelName">
          <el-input v-model="lblForm.labelName" placeholder="请输入标签名称" maxlength="30" />
        </el-form-item>
        <el-form-item label="标签取值" prop="labelField">
          <el-input type="textarea" v-model="lblForm.labelField" placeholder="请输入标签取值" maxlength="-1" rows="3" resize="none" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="lblForm.remark" placeholder="请输入备注" maxlength="-1" rows="2" resize="none" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showLbl=false">取 消</el-button>
        <el-button type="primary" @click="btn_ok_lbl">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog class="custom" title="文件选择" v-if="showfile" :visible.sync="showfile" width="45%" :close-on-click-modal="false">
      <FileSelect ref="FileSelect" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showfile=false">取 消</el-button>
        <el-button type="primary" @click="submitFile">确 定</el-button>
      </div>
    </el-dialog>

    <sql-console ref="sqlConsole"></sql-console>

  </div>
</template>
<script>
import { Sketch } from 'vue-color';
import { dList, addData, editData, delData, lblList, addLabel, editLabel, delLabel,addCopyData } from "@/api/security/riskAnalysis/normal";
import FileSelect from '@/components/FIleSelect'
import serviceKnowledge from "@/api/knowledge.js";
import { listDataSource } from '@/api/metadata/datasource'
import sqlConsole from "./sqlConsole.vue";
export default {
  components: {
    'sketch-picker': Sketch,
    FileSelect,
    sqlConsole
  },
  data () {
    return {
      currentLineData: [],
      uploadUrl: process.env.VUE_APP_BASE_API + "/riskFile/fileupdata",
      // 文件选择弹框
      showfile:false,
      fileList: [],
      delUrl: '',
      sWord: '', //搜索框值
      dataOptions: [],
      tableData: [],
      tableData2: [],
      isNewLbl: true,
      showLbl: false,
      showLblForm: false,
      isSaveOldData: false, //默认选中数据
      // 新增
      dataForm: {
        dataSource: 'database',
        vector: 3,
      }, //输入表单数据
      originalDataForm:{},
      originalID: null,
      lblForm: {},
      dataRules: {
        dataName: [{ required: true, message: "数据名称不能为空", trigger: "blur" }],
        vector: [{ required: true, message: "向量数不能为空", trigger: "blur" }],
        type: [{ required: true, message: "数据类型不能为空", trigger: "blur" }],
        promptName: [{ required: true, message: "角色不能为空", trigger: "change" }],

      },
      dataRules2: {
        dataName: [{ required: true, message: "数据名称不能为空", trigger: "blur" }],
        type: [{ required: true, message: "数据类型不能为空", trigger: "blur" }],
        // line: [{ required: true, message: "表头行数不能为空", trigger: "blur" }],
        // column: [{ required: true, message: "表头列数不能为空", trigger: "blur" }]
      },
      dataRules3: {
        dataName: [{ required: true, message: "数据名称不能为空", trigger: "blur" }],
        type: [{ required: true, message: "数据类型不能为空", trigger: "blur" }],
        xAxis: [{ required: true, message: "X不能为空", trigger: "blur" }],
        yAxis: [{ required: true, message: "Y不能为空", trigger: "blur" }],
        chartType: [{ required: true, message: "图表类型不能为空", trigger: "blur" }]
      },
      lblRules: {
        labelName: [{ required: true, message: "标签名称不能为空", trigger: "blur" }],
        labelField: [{ required: true, message: "标签取值不能为空", trigger: "blur" }]
      },
      chartTypeList: [{
        value: '1',
        label: '饼状图'
      }, {
        value: '2',
        label: '柱状图'
      },
      {
        value: '3',
        label: '折线图'
      }
      ],
      knowledgeList: [],
      promptNameList: [],
      sourceDataOptions: [],
      showColorPicker: false,
      chartcolor: '#5470c6',
      sourceOptions:[{
        value: 'database',
        label: '数据库'
      }, {
        value: 'file',
        label: '文件内容'
      }, {
        value: 'ai',
        label: 'AI问答'
      },
//      {
//        value: 'word',
//        label: '拆分文件'
//      }
      ],
    };
  },
  created () {
    this.getDicts("scy_rap_datatype").then((response) => {
      this.dataOptions = response.data;
    });
    this.getDataSourceList();
    this.getList()
    this.getKnowledgeList()
    // this.getPromptNameList()
    serviceKnowledge.getPromptNameList().then((response) => {
      this.promptNameList = response.data;
    });
  },
  watch: {
    tableData: function () {
      this.$nextTick(function () {
        if (this.isSaveOldData) {
          console.log('保存旧数据')
          this.isSaveOldData = false
        } else {
          if(this.tableData.length != 0){
            this.$refs.dataTable.setCurrentRow(this.tableData[0])
          }else {
            this.btn_new();
          }
        }
      })
    }

  },
  methods: {
    // 获取知识库列表
    async getKnowledgeList() {
      const { code, data } = await serviceKnowledge.getKnowledgeList();
      if (code === 200) {
        const nickName = this.$store.state.user.nickName || "";
        this.knowledgeList = data
          .map((v, index) => {
            return {
              label: v,
              value: index,
            };
          })
          .filter((item) => {
            return !(
              (item.label.startsWith("个人文档-") &&
                item.label !== `个人文档-${nickName}`) ||
              item.label.startsWith("template-")
            );
          });
        console.log("this.knowledgeList", this.knowledgeList)
        // const value = this.$store.state.common.chat;
        // console.log("2222value", this.$store.state.common.chat)
        // this.dataForm.knowledgeBase = value ? value : data[0];
      }
    },

     getPromptNameList(){
       console.log("123")
      const { code, data } = serviceKnowledge.getPromptNameList();
      if (code === 200) {
        console.log("123",data)
      }
    },

    getDataSourceList() {
      listDataSource().then(response => {
        console.log(response)
        if (response.code==200) {
          this.sourceDataOptions = response.data
        }
      })
    },

    changeDataSource(val){
      console.log(1111, val , this.originalDataForm )
        if(val != this.originalDataForm.dataSource ){
          if(val == "database"){
            if(this.dataForm.knowledgeBase){
              this.dataForm.knowledgeBase = "";
            }
            this.dataForm.sourceDocName = "";
            this.dataForm.contentKey = "";
          }else if(val == "file"){
            this.dataForm.knowledgeBase = "";
          }else {
            this.dataForm.sourceDocName = "";
            this.dataForm.contentKey = "";
            this.dataForm.vector = 3;
            if(this.dataForm.knowledgeBase){
              this.dataForm.knowledgeBase = "";
            }
          }
          this.dataForm.dataSql = "";
        }else {
          if(!this.originalID){
            this.dataForm = this.originalDataForm;
          }
        }

    },

    sqlRun(){
      if(!this.dataForm.dataSql){
        this.$message.warning("SQL不能为空！");
        return
      }
      if (this.dataForm.sqlPara) {
        const paramPattern =/^{[^{}\s]+}(,{[^{}\s]+})*$/;
        if (!paramPattern.test(this.dataForm.sqlPara.trim())) {
          this.$message.warning("【SQL参数】格式不正确，请使用 {参数名} 格式，多个参数用逗号分隔，如：{data},{day}");
          return;
        }
      }
      console.log("调用前", this.dataForm.dataSql)
      this.$refs.sqlConsole.openDialog(this.dataForm.knowledgeBase,this.dataForm.sqlPara,this.dataForm.dataSql);
    },
    // 文件格式校验
    handleBeforeUpload (file) {
      let fileType = ['doc', 'docx'];
      let fileExtension = "";
      if (file.name.lastIndexOf(".") > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
      }
      const isTypeOk = fileType.some((type) => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
      console.log(isTypeOk);
      if (!isTypeOk) {
        this.$message.error(`文件格式不正确, 请上传${fileType.join("/")}格式文件!`);
        return false;
      }
    },
    // 文件上传成功处理
    handleFileSuccess (response, file, fileList) {
      console.log(response, 'uploadOk');
      this.dataForm.filepath = response.data.url
    },
    // 文件移除处理
    handleFileRemove (response, file, fileList) {
      // console.log(response, file, fileList);
      this.delUrl = this.dataForm.fileurl
      this.dataForm.filepath = ""
    },
    chgOne (e) {
      console.log(3333333333,e);
      if (e) {
        this.fileList = []
        this.dataForm = JSON.parse(JSON.stringify(e))
        this.originalDataForm = JSON.parse(JSON.stringify(this.dataForm));
        this.originalID =  this.dataForm.id;
        if (this.dataForm.filepath) {
          let obj = {
            name: this.dataForm.dataName,
            url: this.dataForm.filepath
          }
          this.fileList.push(obj)
        }
        if (this.dataForm.chartcolor) {
          this.chartcolor = this.dataForm.chartcolor
        }
        this.showLblForm = true;
        this.getLblList()
      }
    },
    // 添加、修改模板数据
    btn_save () {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (this.showLblForm) { //判断是否第一次保存数据
            // 修改
            let params = {};
            if (this.dataForm.type == 1) {
              params = {
                id: this.dataForm.id,
                templateId: this.$route.query.id,
                dataName: this.dataForm.dataName,
                type: this.dataForm.type,
                sqlPara: this.dataForm.sqlPara,
              }
            } else if (this.dataForm.type == 2) {
              params = {
                id: this.dataForm.id,
                templateId: this.$route.query.id,
                dataName: this.dataForm.dataName,
                type: this.dataForm.type,
                sqlPara: this.dataForm.sqlPara,
                dataSql: this.dataForm.dataSql,
              }
            } else if (this.dataForm.type == 3) {
              params = {
                id: this.dataForm.id,
                templateId: this.$route.query.id,
                dataName: this.dataForm.dataName,
                type: this.dataForm.type,
                sqlPara: this.dataForm.sqlPara,
                dataSql: this.dataForm.dataSql,
                xAxis: this.dataForm.xAxis,
                yAxis: this.dataForm.yAxis,
                chartType: this.dataForm.chartType,
              }
            }

            if (this.dataForm.dataSource == 'file') {
                params.sourceDocName = this.dataForm.sourceDocName,
                params.contentKey = this.dataForm.contentKey
                params.dataSql =  this.dataForm.dataSql;
            }
            if (this.dataForm.dataSource == 'database') {
                if (this.dataForm.sqlPara) {
                  const paramPattern =/^{[^{}\s]+}(,{[^{}\s]+})*$/;
                  if (!paramPattern.test(this.dataForm.sqlPara.trim())) {
                    this.$message.warning("【SQL参数】格式不正确，请使用 {参数名} 格式，多个参数用逗号分隔，如：{data},{day}");
                    return;
                  }
                }
                params.knowledgeBase = this.dataForm.knowledgeBase;
                params.dataSql =  this.dataForm.dataSql;
                params.sourceDocName = '',
                params.contentKey = ''
            }

            if (this.dataForm.dataSource == 'ai') {
                params.knowledgeBase = this.dataForm.knowledgeBase;
                params.dataSql =  this.dataForm.dataSql;
                params.vector =  this.dataForm.vector;
                params.promptName =  this.dataForm.promptName;
                params.sourceDocName = '',
                params.contentKey = ''
            }

                params.dataSource = this.dataForm.dataSource,
                params.sort = this.dataForm.sort;
            addData(params).then(res => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.isSaveOldData = true
                if (this.delUrl) {
                  delTemFile(this.delUrl).then(ress => {
                    console.log(ress);
                    this.getList();
                  })
                } else {
                  this.getList();
                }
              }
            })
          } else {
            // 添加
            this.dataForm.templateId = this.$route.query.id
            if (this.dataForm.dataSource == 'database') {
              if (this.dataForm.sqlPara) {
                const paramPattern =/^{[^{}\s]+}(,{[^{}\s]+})*$/;
                if (!paramPattern.test(this.dataForm.sqlPara.trim())) {
                  this.$message.warning("【SQL参数】格式不正确，请使用 {参数名} 格式，多个参数用逗号分隔，如：{data},{day}");
                  return;
                }
              }
            }

            addData(this.dataForm).then(res => {
              if (res.code == 200) {
                this.$message.success("添加成功");
                this.getList()
                this.btn_new();
              }
            })
          }
        }
      })
    },
    btn_search () {
      this.getList(this.sWord)
    },
    // 新增模板数据（清空表单）
    btn_new () {
      // console.log(22222222222,this.$refs.dataForm);
      this.originalID = null;
      this.originalDataForm = {};
      this.$refs.dataTable.setCurrentRow();
      this.$refs.dataForm.resetFields();
      this.dataForm = {
        id: null,
        templateId: this.$route.query.id,
        dataName: null,
        sourceDocName: '',
        contentKey: null,
      };
      this.chartcolor = '#5470c6';
      this.showLblForm = false;
    },
    // 删除模板数据
    btn_del () {
      if (this.dataForm.id) {
        delData(this.dataForm.id).then(res => {
          if (res.code == 200) {
            this.$message.success("删除成功");
            this.getList()
            this.btn_new()
          }
        })
      } else {
        this.$message({
          message: '请先选中一行数据',
          type: 'warning'
        });
      }
    },
    // 添加、修改标签
    btn_ok_lbl () {
      this.$refs.lblForm.validate((valid) => {
        if (valid) {
          if (this.isNewLbl) {
            // 添加
            this.lblForm.dataId = this.dataForm.id
            addLabel(this.lblForm).then(res => {
              if (res.code == 200) {
                this.$message.success("添加成功");
                this.showLbl = false;
                this.getLblList();
              }
            })
          } else {
            // 修改
            editLabel(this.lblForm).then(res => {
              console.log(res);
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.showLbl = false;
                this.getLblList();
              }
            })
          }
        }
      })
    },
    // 修改标签
    handleEdit (index, e) {
      this.showLbl = true;
      this.isNewLbl = false;
      this.lblForm = JSON.parse(JSON.stringify(e))
    },
    // 删除标签
    handleDelete (index, e) {
      this.$confirm('是否删除该标签？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(index, e);
        delLabel(e.id).then(res => {
          this.$message.success("删除成功");
          this.getLblList()
        })
      }).catch(() => {
        console.log('取消');
      })
    },
    closeDialog () {
      this.$refs.lblForm.resetFields();
      this.lblForm = {
        labelName: null,
        labelField: null,
        remark: null
      }
    },
    getList (e) {
      let dataName = ""
      if (e) {
        dataName = e
      }
      let params = {
        id: this.$route.query.id,
        dataName
      }
      dList(params).then(res => {
        this.tableData = res.data.repDatas
        this.$nextTick(() => {
          $(".lBox").height(this.curHeight())
          $(".rBox").height(this.curHeight())
        })
      })

    },
    getLblList () {
      lblList(this.dataForm.id).then(res => {
        this.tableData2 = res.data
        console.log(this.tableData2);
      })
    },
    colorValueChange (val) {
      console.log(val)
      this.dataForm.chartcolor = val.hex
      this.chartcolor = val.hex
    },
    formatterDT (row) {
      let idx = this.dataOptions.findIndex(item => {
        return item.dictValue == row.type
      })
      return this.dataOptions[idx].dictLabel
    },
    //动态计算高度
    curHeight () {
      return $('.app-main').outerHeight() - 48 - 32;
    },
    selectFile(){
      this.showfile = true
    },
    submitFile(){
        let data = this.$refs.FileSelect.params;
        this.dataForm.dataSql = data.SQL
        this.dataForm.contentKey = data.contentKey
        this.dataForm.sourceDocName = data.resultName
        this.showfile = false
    },

    currentLine(e){
      this.currentLineData = e
    },

    btn_copy(){
      let id = this.currentLineData.id
      if (id) {
        addCopyData(id).then(res => {
          if (res.code == 200) {
            this.$message.success("添加成功");
            this.getList()
            this.btn_new();
          }
        })
      }else {
        this.$message({
          message: '请先选中一行数据',
          type: 'warning'
        });
      }


    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 24px;
  box-sizing: border-box;
  background-color: #f0f2f5;
  .btnStyle.el-button--medium {
    padding: 8px 20px !important;
    height: 32px;
    line-height: 14px;
  }
}
.bigBox {
  display: flex;
  align-items: flex-start;
  .el-col-24 {
    padding: 0 !important;
  }
}
.lBox {
  padding: 16px;
  margin-right: 16px;
  background-color: #fff;
  overflow: auto;
  &__top {
    display: flex;
    margin-bottom: 16px;
  }
}
.ml10 {
  margin-left: 10px;
}
.pr10 {
  padding-right: 10px;
}
.upload-demo {
  ::v-deep .el-upload-dragger {
    width: 100%;
    height: auto;
    padding: 12px 0;
    .el-icon-upload {
      margin: 0;
    }
  }
  ::v-deep .el-upload__text {
    padding: 0 18px;
    line-height: 1.6;
  }
  ::v-deep .el-form-item--medium .el-form-item__content {
    line-height: 20px;
  }
  ::v-deep .el-upload__tip {
    line-height: 20px;
    margin-top: 0;
    text-align: center;
  }
  ::v-deep .el-upload-dragger {
    height: 130px;
  }
  ::v-deep .el-upload-dragger .el-icon-upload {
    margin: 10px 0;
  }
}
.rBox {
  padding: 16px;
  background-color: #fff;
  overflow: auto;
  &__top {
    height: 32px;
    margin-bottom: 10px;
    display: flex;
    font-size: 18px;
    line-height: 32px;
    &__title {
      flex: 1;
      text-align: center;
      background-color: #0189ff;
      color: #fff;
    }
  }
  &__set {
    ::v-deep label {
      font-weight: normal;
    }
    ::v-deep .el-select--medium {
      width: 100%;
    }
  }
  &__label {
    margin-top: 28px;
  }
  &__innerTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    h4 {
      margin: 0 0;
      font-weight: bold;
      span {
        background-color: #0189ff;
        color: #0189ff;
        margin-right: 5px;
      }
    }
  }
  .chart-color {
    display: flex;
    .chart-color-box {
      flex-shrink: 0;
      width: 28px;
      height: 28px;
      margin: 2px;
    }
    .ok-picker {
      margin-top: 2px;
      font-size: 28px;
      color: #0189ff;
    }
  }
  .chart-color-board {
    float: left;
  }
}
::v-deep .custom  .el-dialog{
margin-top: 3vh !important;
}
</style>

