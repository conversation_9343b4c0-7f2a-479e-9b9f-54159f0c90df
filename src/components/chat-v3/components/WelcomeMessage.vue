<template>
  <div class="welcome-message">
    <div class="welcome-title">
<!--      <img :src="systemPic" alt="Welcome" width="32" height="32"/>-->
      {{ computedTitle }}
    </div>
    <div class="welcome-subtitle">{{ subtitle }}</div>
  </div>
</template>

<script>
export default {
  name: 'WelcomeMessage',
  props: {
    subtitle: {
      type: String,
      default: '作为你的智能伙伴，我既能写文案、想点子，又能陪你聊天、答疑解惑。'
    }
  },

  data() {
    return {
      systemPic: "",
      companyName: "",
    };
  },

  computed: {
    // 动态生成标题
    computedTitle() {
      return this.companyName + '·AI助手';
    }
  },

  created() {
    this.getConfigKey("SYSTEM_PIC").then((response) => {
      this.systemPic = response.msg;
    });
    this.getConfigKey("companyName").then((response) => {
      this.companyName = response.msg;
    });
  },
}
</script>

<style lang="scss" scoped>
.welcome-message {
  background: url('../icons/welcome-bg.png') no-repeat center center;
  background-size: cover;
  border-radius: 16px;
  padding: 26px 50px;
  color: white;
  margin-bottom: 20px;
}

.welcome-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;

  img {
    margin-right: 10px;
  }
}

.welcome-subtitle {
  font-size: 14px;
  margin-top: 8px;
}
</style>
