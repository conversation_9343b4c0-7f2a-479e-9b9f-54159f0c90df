<template>
  <div class="chat-message-container">
    <div class="chat-message">
      <div class="avatar avatar-bot">
        <!-- 使用当前知识库的图标，如果没有则使用默认图标 -->
        <img
          v-if="currentKnowledgeIcon"
          :src="currentKnowledgeIcon"
          alt="AI Assistant"
          width="22"
          height="16"
          @error="handleImageError"
        />
        <img
          v-else
          :src="systemPic"
          alt="AI Assistant"
          width="16"
          height="16"
        />
      </div>
      <div class="message-content">
        <!-- 加载状态 - 只在没有任何内容时显示 -->
        <div v-if="!message.done && !message.content" class="loading-content">
          <div class="loading-text">AI正在思考中...</div>
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>

        <!-- 消息内容 - 有内容就显示，不管是否完成 -->
        <div v-if="message.content || message.done" class="message-body">
          <!-- 调试信息 -->
          <!-- <div style="font-size: 10px; color: #999;">
            内容长度: {{ (message.content || '').length }}, 完成状态: {{ message.done }}
          </div> -->
          <!-- 消息发送者（如果有的话） -->
          <div v-if="message.sender" class="message-sender">{{ message.sender }}</div>

          <!-- 文本回答 -->
          <v-md-preview :text="message.content"></v-md-preview>

          <!-- 流式输入指示器 -->
          <div v-if="message.content && !message.done" class="streaming-indicator">
            <span class="cursor-blink">|</span>
          </div>

          <!-- 表格数据 -->
          <div
            v-if="message.tableData"
            :id="tableElementId"
            class="table-content"
            v-html="message.tableData"
          ></div>

          <!-- 图表 -->
          <div
            v-if="message.chartData"
            :id="chartElementId"
            class="chart-content"
          ></div>

          <!-- 文件结果 -->
          <div v-if="message.result" class="file-result">
            <el-link size="mini" type="success" :underline="false">
              <i class="el-icon-view el-icon--right"></i>
              根据您的需求，调用接口生成的文件:
            </el-link>
            <el-link type="primary" @click="previewFile(message.result)">
              {{ getFileName(message.result) }}
            </el-link>
            <el-link type="primary" @click="downloadFile(message.result)">
              下载
            </el-link>
          </div>

          <!-- 文档来源 -->
          <div v-if="processedDocs && processedDocs.length > 0" class="file-sources">
            <el-collapse accordion>
              <el-collapse-item>
                <template slot="title">
                  <el-tag size="small" type="primary">文件来源</el-tag>
                </template>
                <div class="docs-list">
                  <div
                    v-for="(doc, index) in processedDocs"
                    :key="index"
                    class="doc-item"
                  >
                    <span class="doc-index">{{ index + 1 }}.</span>
                    <el-link
                      type="primary"
                      :underline="false"
                      @click="handleDocPreview(doc.link, doc.fileName)"
                      class="doc-link"
                    >
                      {{ doc.fileName }}
                    </el-link>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息操作工具栏 -->
    <MessageToolbar
      v-if="message.done"
      :message="message"
      :message-index="messageIndex"
      :clipboard-target="'#' + textElementId"
      @copy="handleCopy"
      @regenerate="handleRegenerate"
      @delete="handleDelete"
      @like="handleLike"
      @pause="handlePause"
      @voice-play="handleVoicePlay"
    />
  </div>
</template>

<script>
import MessageToolbar from './MessageToolbar.vue'
import { triggerMermaidRender } from '../../utils/index.js'
import { mapState, mapGetters } from 'vuex'
import fileService from '../../services/FileService.js'

export default {
  name: 'AssistantMessage',
  components: {
    MessageToolbar
  },
  props: {
    // 消息对象
    message: {
      type: Object,
      required: true
    },
    // 消息索引
    messageIndex: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      systemPic: "",
    };
  },
  watch: {
    // 监听消息内容变化
    'message.content': {
      handler() {
        console.log('Message content changed, triggering mermaid render')
        this.$nextTick(() => {
          // 触发 mermaid 图表渲染
          this.triggerMermaidRender();
        });
      },
      immediate: false
    },
    // 监听消息完成状态
    'message.done': {
      handler(newVal) {
        if (newVal) {
          console.log('Message done, triggering mermaid render')
          this.$nextTick(() => {
            // 消息完成时，触发 mermaid 图表渲染
            this.triggerMermaidRender();
          });
        }
      },
      immediate: false
    }
  },

  created() {
    this.getConfigKey("SYSTEM_PIC").then((response) => {
      this.systemPic = response.msg;
    });
  },

  computed: {
    ...mapState('chat', ['knowledgeList', 'currentKnowledgeId']),
    ...mapGetters('chat', ['currentKnowledge']),

    textElementId() {
      return `text-${this.messageIndex}`;
    },
    tableElementId() {
      return `table-${this.messageIndex}`;
    },
    chartElementId() {
      return `chart-${this.messageIndex}`;
    },

    // 获取当前知识库的图标
    currentKnowledgeIcon() {
      if (this.currentKnowledge && this.currentKnowledge.image) {
        return this.currentKnowledge.image;
      }
      return null;
    },

    // 处理文档来源数据
    processedDocs() {
      if (!this.message.docs) return [];

      // 如果是字符串格式的HTML，尝试解析
      if (typeof this.message.docs === 'string') {
        return this.parseDocsFromHtml(this.message.docs);
      }

      // 如果是数组格式，直接处理
      if (Array.isArray(this.message.docs)) {
        return this.message.docs.map(doc => {
          if (typeof doc === 'string') {
            const parts = doc.split(',').map(part => part.trim());
            const [fileName, link, ...contentParts] = parts;
            return {
              fileName: fileName || doc,
              link: link || '',
              content: contentParts.join(',') || ''
            };
          } else if (typeof doc === 'object' && doc !== null) {
            return {
              fileName: doc.fileName || doc.name || '未知文件',
              link: doc.link || doc.url || '',
              content: doc.content || ''
            };
          }
          return null;
        }).filter(doc => doc !== null);
      }

      return [];
    }
  },
  methods: {
    handleCopy(data) {
      this.$emit('copy', data);
    },

    handleRegenerate(data) {
      this.$emit('regenerate', data);
    },

    handleDelete(data) {
      this.$emit('delete', data);
    },

    handleLike(data) {
      this.$emit('like', data);
    },

    handlePause(data) {
      this.$emit('pause', data);
    },

    handleVoicePlay(data) {
      this.$emit('voice-play', data);
    },

    previewFile(filePath) {
      this.$emit('preview-file', filePath);
    },

    downloadFile(filePath) {
      this.$emit('download-file', filePath);
    },

    // 处理图片加载错误
    handleImageError(event) {
      // 图片加载失败时，替换为默认图标
      event.target.src = require('../../icons/logo.svg');
    },

    previewDoc(docLink, fileName) {
      this.$emit('preview-doc', { link: docLink, fileName });
    },

    getFileName(filePath) {
      if (!filePath) return '';
      return filePath.substring(filePath.lastIndexOf('/') + 1);
    },

    // 触发 Mermaid 图表渲染
    triggerMermaidRender() {
      triggerMermaidRender();
    },

    // 处理文档预览
    async handleDocPreview(link, fileName) {
      try {
        if (!link) {
          this.$message.warning('文件链接不存在');
          return;
        }

        // 使用fileService预览文件
        await fileService.previewKnowledgeFile(link, fileName);
      } catch (error) {
        console.error('文件预览失败:', error);
        this.$message.error('文件预览失败，请稍后重试');
      }
    },

    // 从HTML字符串中解析文档信息
    parseDocsFromHtml(htmlString) {
      try {
        // 创建临时DOM元素来解析HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlString;

        const docs = [];
        const links = tempDiv.querySelectorAll('a');

        links.forEach((link) => {
          const fileName = link.textContent.trim();
          const href = link.getAttribute('href') || '';

          if (fileName) {
            docs.push({
              fileName,
              link: href,
              content: ''
            });
          }
        });

        return docs;
      } catch (error) {
        console.error('解析文档HTML失败:', error);
        return [];
      }
    }

  }
}
</script>

<style>
.github-markdown-body {
  padding: 0;
}
</style>

<style lang="scss" scoped>
// 根据设计稿的CSS变量
:root {
  --bg-color: #F7F8FB;
  --sidebar-bg-color: #FFFFFF;
  --header-bg-color: #FFFFFF;
  --chat-bubble-user-bg: #FFFFFF;
  --chat-bubble-bot-bg: #FFFFFF;
  --input-area-bg: #FFFFFF;
  --border-color: #E8E8E8;
  --text-primary: #313233;
  --text-secondary: #949699;
  --text-link: #0676DB;
  --accent-color: #2D8CF0;
  --placeholder-text: #C6C8CC;
  --button-hover-bg: #F5F6F7;
}

.chat-message-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: var(--chat-bubble-bot-bg, #FFFFFF);
  border: 1px solid var(--border-color, #E8E8E8);
  border-radius: 4px;
  padding: 15px 20px;

  .chat-message {
    display: flex;
    gap: 12px;
    align-items: flex-start;

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%; // 和ChatHeader保持一致的圆形
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;

      &.avatar-bot {
        background: linear-gradient(to bottom right, #26a4fe, #9038f7, #ff68cf); // 和ChatHeader完全一致
      }
    }

    .message-content {
      display: flex;
      flex-direction: column;
      gap: 12px;
      flex-grow: 1;
    }
  }
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 12px;

  .loading-text {
    color: #666;
    font-size: 14px;
  }

  .loading-dots {
    display: flex;
    gap: 4px;

    span {
      width: 6px;
      height: 6px;
      background: #409eff;
      border-radius: 50%;
      animation: loading-bounce 1.4s ease-in-out infinite both;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

.message-body {
  .message-sender {
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary, #313233);
    margin-bottom: 12px;
  }

  .message-text {
    font-size: 14px;
    line-height: 24px;
    color: var(--text-primary, #313233);

    // Markdown样式
    h1, h2, h3, h4, h5, h6 {
      margin: 16px 0 8px 0;
      font-weight: 600;
    }

    p {
      margin: 8px 0;
    }

    code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 0.9em;
    }

    pre {
      background: #f8f8f8;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 12px;
      overflow-x: auto;
      margin: 12px 0;

      code {
        background: none;
        padding: 0;
      }
    }

    ul, ol {
      margin: 8px 0;
      padding-left: 24px;
    }

    blockquote {
      border-left: 4px solid #409eff;
      margin: 12px 0;
      padding: 8px 16px;
      background: #f8f9fa;
      color: #666;
    }
  }
}

.table-content {
  margin-top: 16px;

  :deep(.result-table) {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;

    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e8e8e8;
    }

    th {
      background: #f8f9fa;
      font-weight: 600;
      color: #333;
    }

    tr:last-child td {
      border-bottom: none;
    }

    tr:hover {
      background: #f8f9fa;
    }
  }
}

.chart-content {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fff;
}

.file-result {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;

  .el-link {
    margin-right: 12px;
  }
}

.file-sources {
  padding-top: 12px;
  border-top: 1px solid var(--border-color, #E8E8E8);
  margin-top: 16px;

  :deep(.el-collapse) {
    border: none;

    .el-collapse-item {
      border-bottom: none;

      .el-collapse-item__header {
        height: auto;
        line-height: 1.5;
        padding: 8px 0;
        border-bottom: none;
        background: transparent;
        font-size: 14px;

        &:hover {
          background: #f5f7fa;
        }
      }

      .el-collapse-item__content {
        padding: 12px 0 0 0;
      }
    }
  }

  .docs-list {
    .doc-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 6px 0;

      &:last-child {
        margin-bottom: 0;
      }

      .doc-index {
        color: #409eff;
        font-weight: 500;
        margin-right: 8px;
        min-width: 20px;
      }

      .doc-link {
        flex: 1;
        font-size: 14px;

        &:hover {
          color: #66b1ff;
        }
      }
    }
  }
}

// 流式输入指示器样式
.streaming-indicator {
  display: inline-block;
  margin-left: 2px;

  .cursor-blink {
    animation: blink 1s infinite;
    font-weight: bold;
    color: #666;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

// Mermaid 图表样式
:deep(.mermaid-container) {
  margin: 16px 0;
  text-align: center;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;

  &.mermaid-rendered {
    background: transparent;
    border: none;
    padding: 0;
  }

  svg {
    max-width: 100%;
    height: auto;
  }

  .mermaid-error {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;
  }
}

// Think标签样式
:deep(.think-container) {
  margin: 16px 0;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #f8f9fa;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .think-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid #e1e5e9;

    &:hover {
      background: linear-gradient(135deg, #e8eaed 0%, #dadce0 100%);
      transform: translateY(-1px);
    }

    .think-icon {
      margin-right: 10px;
      font-size: 18px;
      filter: grayscale(0.3);
    }

    .think-title {
      flex: 1;
      font-weight: 600;
      color: #5f6368;
      font-size: 14px;
      letter-spacing: 0.5px;
    }

    .think-toggle {
      font-size: 14px;
      color: #5f6368;
      transition: transform 0.3s ease;
      font-weight: bold;
    }
  }

  .think-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    .think-text {
      padding: 20px;
      color: #3c4043;
      font-size: 14px;
      line-height: 1.7;
      white-space: pre-wrap;
      background: #ffffff;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

      // 添加一些文本样式
      strong {
        color: #1a73e8;
        font-weight: 600;
      }

      em {
        color: #ea4335;
        font-style: normal;
        background: rgba(234, 67, 53, 0.1);
        padding: 2px 4px;
        border-radius: 3px;
      }
    }
  }

  &.expanded {
    border-color: #1a73e8;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.15);

    .think-content {
      max-height: 600px;
    }

    .think-toggle {
      transform: rotate(180deg);
      color: #1a73e8;
    }

    .think-header {
      background: linear-gradient(135deg, #e8f0fe 0%, #d2e3fc 100%);
      border-bottom-color: #1a73e8;

      .think-title {
        color: #1a73e8;
      }
    }
  }
}

/* Mermaid 图表样式 */
.message-text .mermaid {
  text-align: center;
  margin: 16px 0;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #e1e4e8;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
