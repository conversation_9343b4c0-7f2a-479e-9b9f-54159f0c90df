<template>
  <div class="chat-main-container">
    <main class="chat-area">
      <div class="content-area">
        <WelcomeSection
          v-if="messages.length === 0"
          ref="welcomeSection"
        />
        <MessageList v-else />
      </div>

      <ChatInput @refresh-history="handleRefreshHistory"/>
      <SetHelp ref="setHelp" @updateHelpList="handleUpdateHelpList" />
    </main>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import WelcomeSection from './WelcomeSection.vue';
import MessageList from './MessageList.vue';
import ChatInput from './ChatInput.vue';
import SetHelp from './SetHelp.vue';

export default {
  name: 'ChatMainArea',
  components: {
    WelcomeSection,
    MessageList,
    ChatInput,
    SetHelp
  },

  computed: {
    ...mapState('chat-v3', ['messages'])
  },

  methods: {
    handleUpdateHelpList() {
      // 通知 WelcomeSection 刷新常用问题
      if (this.$refs.welcomeSection) {
        this.$refs.welcomeSection.refreshCommonQuestions();
      }
    },
    handleRefreshHistory() {
      console.log('####handleRefreshHistory###:')
      // 向上传递历史列表刷新事件
      this.$emit('refresh-history');
    }
  }
};
</script>

<style lang="scss" scoped>
.chat-main-container {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.chat-area {
  flex: 1;
  min-width: 0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
</style>
