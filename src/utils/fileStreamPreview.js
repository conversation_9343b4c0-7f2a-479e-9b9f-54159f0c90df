// import { Base64 } from 'js-base64';
// export const preview = async (id,name)=>{
//   const ipUrl= 'http://*************'
//   let originUrl = `${ipUrl}/prod-api/fileCenter/version/download/${id}`; //要预览文件的访问地址
//   console.log(originUrl,'预览访问地址')
//   let previewUrl = `${originUrl}?fullfilename=${name}`
//   window.open('http://*************:8012/onlinePreview?url='+encodeURIComponent(Base64.encode(previewUrl)));
// }

import { Base64 } from "js-base64";
import { getConfigKey } from '@/api/system/config'; // 导入 getConfigKey 函数

export const preview = async (originUrl, name, url1, url2) => {
  try {
    // 设置 url1 的默认值
    if (!url1) {
      url1 = "http://*************";
    }

    // 如果没有传入 url2，则从接口获取 kkFileView 配置
    if (!url2) {
      const response = await getConfigKey("kkFileView");
      url2 = response.msg;
    }

    console.log("原始地址", originUrl);
    let previewUrl = `${originUrl}&fullfilename=${name}`;
    console.log("预览地址", previewUrl);

    const finalUrl = url2 + "/onlinePreview?url=" + encodeURIComponent(Base64.encode(previewUrl));

    window.open(finalUrl);

    console.log("url1", url1);
    console.log("url2", url2);
    console.log("previewUrl", previewUrl);
    console.log("最后地址", finalUrl);

  } catch (error) {
    console.error("文件预览失败:", error);
    // 如果接口调用失败，使用默认值
    if (!url1) url1 = "http://*************";
    if (!url2) url2 = "http://192.168.0.170:8012";

    let previewUrl = `${originUrl}&fullfilename=${name}`;
    const finalUrl = url2 + "/onlinePreview?url=" + encodeURIComponent(Base64.encode(previewUrl));
    window.open(finalUrl);
  }
};
